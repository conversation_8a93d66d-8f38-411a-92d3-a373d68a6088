#!/bin/bash
# Script de reparación Git para VPS - SOLO PARA REPARAR PROBLEMAS
# Fecha: 2025-09-06
# IMPORTANTE: Este script es SOLO para reparar un repositorio corrupto
# Para actualizaciones normales, usa solo: git pull origin dev_database

echo "=== SCRIPT DE REPARACIÓN DE REPOSITORIO CORRUPTO ==="
echo "⚠️  ATENCIÓN: Este script elimina y re-clona el repositorio"
echo "⚠️  Solo úsalo si el repositorio está corrupto"
echo "⚠️  Para actualizaciones normales, usa solo 'git pull'"
echo ""
read -p "¿Estás seguro de que el repositorio está corrupto? (s/n): " -n 1 -r
echo ""
if [[ ! $REPLY =~ ^[Ss]$ ]]; then
    echo "Operación cancelada. Para actualizaciones normales usa:"
    echo "cd /var/www/html/Reac_fuse_v2"
    echo "git pull origin dev_database"
    exit 1
fi

echo "=== INICIANDO REPARACIÓN DEL REPOSITORIO GIT ==="

# Navegar al directorio web
cd /var/www/html
echo "Directorio actual: $(pwd)"

# Verificar contenido actual
echo "=== Contenido actual de /var/www/html ==="
ls -la

# Verificar si existe el directorio Reac_fuse_v2
if [ -d "Reac_fuse_v2" ]; then
    echo "✓ El directorio Reac_fuse_v2 existe"
    cd Reac_fuse_v2
    
    # Verificar si es un repositorio git válido
    if [ -d ".git" ]; then
        echo "✓ Es un repositorio git válido"
        echo "¿Estás seguro de que está corrupto? Probemos primero un pull normal..."
        echo "Estado actual del repositorio:"
        git status
        echo "Ramas disponibles:"
        git branch -a
        
        echo "Intentando pull normal..."
        git checkout dev_database
        if git pull origin dev_database; then
            echo "✅ ¡El pull funcionó! El repositorio NO estaba corrupto."
            echo "En el futuro, usa solo: git pull origin dev_database"
            exit 0
        else
            echo "❌ El pull falló. Procediendo con reparación..."
        fi
    fi
    
    echo "Eliminando directorio corrupto..."
    cd ..
    rm -rf Reac_fuse_v2
fi

echo "Clonando repositorio fresco desde GitHub con API token..."
git clone https://<EMAIL>/nicomagno17/Reac_fuse_v2.git
cd Reac_fuse_v2

echo "Configurando rama dev_database..."
git checkout dev_database
git pull origin dev_database

# Verificar estado final
echo "=== ESTADO FINAL ==="
echo "Directorio actual: $(pwd)"
echo "Estado del repositorio:"
git status
echo "Rama actual:"
git branch
echo "Último commit:"
git log --oneline -1

# Verificar permisos
echo "=== VERIFICANDO PERMISOS ==="
ls -la
echo "Propietario del directorio:"
ls -ld .

echo "=== REPARACIÓN COMPLETADA ==="