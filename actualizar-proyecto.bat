@echo off
echo ========================================
echo    ACTUALIZADOR AUTOMATICO REACT FUSE
echo ========================================
echo.

REM Configuración de variables
set SSH_KEY=%USERPROFILE%\.ssh\id_ed25519_server
set SSH_PORT=22222
set SSH_USER=root
set SSH_HOST=**************
set PROJECT_PATH=/var/www/html/Reac_fuse_v2
set BACKUP_PATH=/var/www/html/backups

echo [INFO] Iniciando proceso de actualización...
echo [INFO] Servidor: %SSH_HOST%
echo [INFO] Proyecto: %PROJECT_PATH%
echo.

REM Verificar que existe la clave SSH
if not exist "%SSH_KEY%" (
    echo [ERROR] No se encontró la clave SSH en: %SSH_KEY%
    echo [ERROR] Asegúrate de que la clave SSH esté en la ubicación correcta.
    pause
    exit /b 1
)

echo [INFO] Conectando al servidor...
echo.

REM Ejecutar comandos en el servidor remoto
ssh -i "%SSH_KEY%" -p %SSH_PORT% %SSH_USER%@%SSH_HOST% "cd %PROJECT_PATH% && echo '[SERVIDOR] Conectado exitosamente al VPS' && echo '[SERVIDOR] Navegando al directorio del proyecto...' && git status && echo '[SERVIDOR] Creando backup de la versión actual...' && mkdir -p %BACKUP_PATH% && BACKUP_NAME=backup_$(date +%%Y%%m%%d_%%H%%M%%S) && cp -r %PROJECT_PATH% %BACKUP_PATH%/$BACKUP_NAME && echo '[SERVIDOR] Backup creado en: %BACKUP_PATH%/$BACKUP_NAME' && echo '[SERVIDOR] Obteniendo últimos cambios del repositorio...' && git fetch origin && echo '[SERVIDOR] Verificando rama actual...' && CURRENT_BRANCH=$(git branch --show-current) && echo '[SERVIDOR] Rama actual: '$CURRENT_BRANCH && echo '[SERVIDOR] Haciendo pull de la rama actual...' && git pull origin $CURRENT_BRANCH && echo '[SERVIDOR] Verificando permisos de archivos...' && chmod -R 755 %PROJECT_PATH%/assets/ && chown -R www-data:www-data %PROJECT_PATH% && echo '[SERVIDOR] Reiniciando Nginx...' && systemctl reload nginx && echo '[SERVIDOR] ✅ Actualización completada exitosamente' && echo '[SERVIDOR] 🌐 La aplicación está disponible en: http://%SSH_HOST%'"

REM Verificar el resultado del comando SSH
if %ERRORLEVEL% equ 0 (
    echo.
    echo ========================================
    echo ✅ ACTUALIZACIÓN COMPLETADA EXITOSAMENTE
    echo ========================================
    echo.
    echo La aplicación React Fuse ha sido actualizada en el servidor.
    echo Puedes verificar los cambios en: http://%SSH_HOST%
    echo.
) else (
    echo.
    echo ========================================
    echo ❌ ERROR DURANTE LA ACTUALIZACIÓN
    echo ========================================
    echo.
    echo Revisa los mensajes anteriores para más detalles.
    echo El backup automático debería haber restaurado la versión anterior.
    echo.
)

echo Presiona cualquier tecla para continuar...
pause >nul
