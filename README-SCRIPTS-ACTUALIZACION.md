# 🚀 Scripts de Actualización Automática - React Fuse

Este conjunto de scripts te permite actualizar automáticamente tu aplicación React Fuse en el VPS sin necesidad de conectarte manualmente por SSH.

## 📁 Archivos Incluidos

### 1. `actualizar-proyecto.bat` 
**Script básico para Windows (Batch)**
- Actualización rápida con pull automático
- Creación de backups automáticos
- Restauración en caso de error
- Reinicio automático de Nginx

### 2. `actualizar-proyecto-avanzado.bat`
**Script avanzado con menú interactivo (Batch)**
- <PERSON>ú con múltiples opciones
- Actualización con rebuild completo
- Cambio de ramas
- Gestión de backups
- Consulta de estado del repositorio

### 3. `Actualizar-Proyecto.ps1`
**Script moderno en PowerShell**
- Interfaz colorida y moderna
- Parámetros de línea de comandos
- Mejor manejo de errores
- Funciones modulares

## 🔧 Configuración Inicial

### Requisitos Previos
1. **SSH configurado** con la clave `id_ed25519_server` en `%USERPROFILE%\.ssh\`
2. **Git configurado** en el VPS
3. **Permisos de sudo** para el usuario root
4. **Nginx funcionando** en el VPS

### Verificar Configuración
```bash
# Verificar que puedes conectarte por SSH
ssh -i ~/.ssh/id_ed25519_server -p 22222 root@**************

# Verificar que el proyecto existe
ls -la /var/www/html/Reac_fuse_v2
```

## 🚀 Uso de los Scripts

### Script Básico (actualizar-proyecto.bat)
```cmd
# Ejecutar desde el directorio del proyecto
actualizar-proyecto.bat
```

**Lo que hace:**
1. Se conecta al VPS por SSH
2. Crea un backup automático con timestamp
3. Hace `git pull` de la rama actual
4. Ajusta permisos de archivos
5. Reinicia Nginx
6. En caso de error, restaura el backup

### Script Avanzado (actualizar-proyecto-avanzado.bat)
```cmd
# Ejecutar y seguir el menú interactivo
actualizar-proyecto-avanzado.bat
```

**Opciones disponibles:**
1. **Actualización rápida** - Pull simple y rápido
2. **Actualización con rebuild** - Incluye `npm install` y `npm run build`
3. **Cambiar rama** - Cambia a una rama específica
4. **Ver estado** - Muestra el estado del repositorio
5. **Restaurar backup** - Restaura desde un backup específico
6. **Limpiar backups** - Elimina backups antiguos (>7 días)

### Script PowerShell (Actualizar-Proyecto.ps1)
```powershell
# Menú interactivo
.\Actualizar-Proyecto.ps1

# Actualización rápida directa
.\Actualizar-Proyecto.ps1 -Accion quick

# Actualización con rebuild
.\Actualizar-Proyecto.ps1 -Accion rebuild -Forzar

# Cambiar a rama específica
.\Actualizar-Proyecto.ps1 -Accion branch -Rama "desarrollo"

# Ver estado del repositorio
.\Actualizar-Proyecto.ps1 -Accion status
```

## 📋 Flujo de Trabajo Recomendado

### Para Actualizaciones Menores
```cmd
# Usar el script básico para cambios simples
actualizar-proyecto.bat
```

### Para Actualizaciones con Dependencias
```cmd
# Usar el script avanzado, opción 2
actualizar-proyecto-avanzado.bat
# Seleccionar opción 2 (Actualización con rebuild)
```

### Para Cambios de Rama
```powershell
# Usar PowerShell para mayor control
.\Actualizar-Proyecto.ps1 -Accion branch -Rama "nueva-rama"
```

## 🛡️ Características de Seguridad

### Backups Automáticos
- **Ubicación**: `/var/www/html/backups/`
- **Formato**: `backup_YYYYMMDD_HHMMSS`
- **Restauración automática** en caso de error

### Verificaciones
- ✅ Existencia de clave SSH
- ✅ Conectividad al servidor
- ✅ Estado del repositorio Git
- ✅ Permisos de archivos
- ✅ Estado de Nginx

### Rollback Automático
Si el `git pull` falla, el script automáticamente:
1. Restaura el backup creado
2. Reinicia Nginx
3. Notifica el error

## 🔍 Solución de Problemas

### Error: "No se encontró la clave SSH"
```cmd
# Verificar ubicación de la clave
dir %USERPROFILE%\.ssh\id_ed25519_server

# Si no existe, copiarla desde donde la tengas
copy "ruta\a\tu\clave" "%USERPROFILE%\.ssh\id_ed25519_server"
```

### Error: "Permission denied"
```bash
# Verificar permisos de la clave SSH (en Git Bash o WSL)
chmod 600 ~/.ssh/id_ed25519_server
```

### Error: "Git pull failed"
```bash
# Conectarse manualmente y verificar
ssh -i ~/.ssh/id_ed25519_server -p 22222 root@**************
cd /var/www/html/Reac_fuse_v2
git status
git pull origin main
```

### Error: "Nginx reload failed"
```bash
# Verificar configuración de Nginx
nginx -t
systemctl status nginx
```

## 📊 Logs y Monitoreo

### Ubicaciones de Logs
- **Nginx Access**: `/var/log/nginx/react-fuse.access.log`
- **Nginx Error**: `/var/log/nginx/react-fuse.error.log`
- **Backups**: `/var/www/html/backups/`

### Comandos de Monitoreo
```bash
# Ver logs de Nginx en tiempo real
tail -f /var/log/nginx/react-fuse.access.log

# Ver últimos errores
tail -20 /var/log/nginx/react-fuse.error.log

# Listar backups disponibles
ls -la /var/www/html/backups/
```

## 🎯 Mejores Prácticas

1. **Siempre hacer backup** antes de actualizaciones importantes
2. **Probar en horarios de bajo tráfico**
3. **Verificar la aplicación** después de cada actualización
4. **Mantener backups limpios** (eliminar antiguos regularmente)
5. **Documentar cambios importantes** en el commit

## 🔗 Enlaces Útiles

- **Aplicación**: http://**************
- **Repositorio**: https://github.com/nicomagno17/Reac_fuse_v2.git
- **Documentación Nginx**: `/etc/nginx/sites-available/react-fuse`

---

**¡Listo!** Con estos scripts puedes mantener tu aplicación React Fuse actualizada de manera rápida y segura. 🚀
