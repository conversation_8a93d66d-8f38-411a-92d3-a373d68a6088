# Script PowerShell para corregir problemas de despliegue en VPS
# Proyecto: React Fuse v2 - Renovatio

param(
    [switch]$SkipBuild = $false,
    [switch]$Force = $false
)

# Configuración
$Config = @{
    VPSHost = "**************"
    VPSUser = "root"
    VPSPort = "22222"
    SSHKey = "$env:USERPROFILE\.ssh\id_ed25519_server"
    ProjectPath = "/var/www/html/Reac_fuse_v2"
    BackupPath = "/var/www/html/backups"
}

# Funciones de utilidad
function Write-Status {
    param([string]$Message)
    Write-Host "[INFO] $Message" -ForegroundColor Blue
}

function Write-Success {
    param([string]$Message)
    Write-Host "[SUCCESS] $Message" -ForegroundColor Green
}

function Write-Warning {
    param([string]$Message)
    Write-Host "[WARNING] $Message" -ForegroundColor Yellow
}

function Write-Error {
    param([string]$Message)
    Write-Host "[ERROR] $Message" -ForegroundColor Red
}

function Invoke-SSHCommand {
    param([string]$Command)
    
    $sshArgs = @(
        "-i", $Config.SSHKey,
        "-p", $Config.VPSPort,
        "$($Config.VPSUser)@$($Config.VPSHost)",
        $Command
    )
    
    & ssh @sshArgs
    return $LASTEXITCODE -eq 0
}

function Copy-ToVPS {
    param(
        [string]$LocalPath,
        [string]$RemotePath
    )
    
    $scpArgs = @(
        "-i", $Config.SSHKey,
        "-P", $Config.VPSPort,
        "-r", $LocalPath,
        "$($Config.VPSUser)@$($Config.VPSHost):$RemotePath"
    )
    
    & scp @scpArgs
    return $LASTEXITCODE -eq 0
}

Write-Status "Iniciando correccion de problemas de despliegue..."

# 1. Verificar herramientas necesarias
Write-Status "1. Verificando herramientas necesarias..."
if (-not (Get-Command ssh -ErrorAction SilentlyContinue)) {
    Write-Error "SSH no está disponible. Instala OpenSSH o Git Bash."
    exit 1
}

if (-not (Get-Command scp -ErrorAction SilentlyContinue)) {
    Write-Error "SCP no está disponible. Instala OpenSSH o Git Bash."
    exit 1
}

# 2. Verificar conexión SSH
Write-Status "2. Verificando conexión SSH..."
if (Invoke-SSHCommand "echo 'Conexión SSH exitosa'") {
    Write-Success "Conexión SSH establecida"
} else {
    Write-Error "No se pudo conectar al VPS"
    exit 1
}

# 3. Crear backup del estado actual
Write-Status "3. Creando backup del estado actual..."
$BackupName = "backup-$(Get-Date -Format 'yyyyMMdd-HHmmss')"
$BackupCommand = "mkdir -p $($Config.BackupPath) && cp -r $($Config.ProjectPath) $($Config.BackupPath)/$BackupName"
if (Invoke-SSHCommand $BackupCommand) {
    Write-Success "Backup creado en $($Config.BackupPath)/$BackupName"
} else {
    Write-Warning "No se pudo crear backup, continuando..."
}

# 4. Verificar y compilar proyecto localmente
if (-not $SkipBuild) {
    Write-Status "4. Verificando build local..."
    if (-not (Test-Path "build\index.html")) {
        Write-Warning "Build local no encontrado. Compilando..."
        npm run build
        if ($LASTEXITCODE -ne 0) {
            Write-Error "Error en la compilación"
            exit 1
        }
        Write-Success "Build local completado"
    } else {
        Write-Success "Build local encontrado"
    }
} else {
    Write-Status "4. Omitiendo compilación local (SkipBuild activado)"
}

# 5. Crear configuración de Nginx corregida
Write-Status "5. Creando configuración de Nginx corregida..."
$NginxConfig = @"
server {
    listen 80;
    listen [::]:80;
    
    server_name ************** _;
    
    # Directorio donde están los archivos de la aplicación React
    root /var/www/html/Reac_fuse_v2;
    index index.html;
    
    # Incluir tipos MIME
    include /etc/nginx/mime.types;
    
    # Configuración para archivos estáticos
    location / {
        try_files `$uri `$uri/ /index.html;
        
        # Headers de seguridad
        add_header X-Frame-Options SAMEORIGIN;
        add_header X-XSS-Protection "1; mode=block";
        add_header X-Content-Type-Options nosniff;
    }
    
    # Configuración específica para archivos JavaScript
    location ~* \.js`$ {
        add_header Content-Type application/javascript;
        expires 1y;
        add_header Cache-Control "public, immutable";
        access_log off;
    }
    
    # Configuración para archivos CSS
    location ~* \.css`$ {
        add_header Content-Type text/css;
        expires 1y;
        add_header Cache-Control "public, immutable";
        access_log off;
    }
    
    # Configuración para fuentes
    location ~* \.(woff|woff2|ttf|eot|otf)`$ {
        add_header Content-Type font/woff;
        expires 1y;
        add_header Cache-Control "public, immutable";
        access_log off;
    }
    
    # Configuración para imágenes
    location ~* \.(png|jpg|jpeg|gif|ico|svg)`$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        access_log off;
    }
    
    # No cachear el index.html para asegurar actualizaciones
    location = /index.html {
        add_header Cache-Control "no-cache, no-store, must-revalidate";
        add_header Pragma "no-cache";
        add_header Expires "0";
    }
    
    # Logs específicos
    access_log /var/log/nginx/react-app.access.log;
    error_log /var/log/nginx/react-app.error.log;
}
"@

$NginxConfig | Out-File -FilePath "nginx-fixed.conf" -Encoding UTF8
Write-Success "Configuración de Nginx creada"

# 6. Subir archivos actualizados
Write-Status "6. Subiendo archivos al VPS..."

# Subir build
if (Test-Path "build") {
    if (Copy-ToVPS "build\*" $Config.ProjectPath) {
        Write-Success "Archivos de build subidos"
    } else {
        Write-Error "Error al subir archivos de build"
        exit 1
    }
} else {
    Write-Warning "Directorio build no encontrado, omitiendo..."
}

# Subir configuración de Nginx
if (Copy-ToVPS "nginx-fixed.conf" "/tmp/") {
    Write-Success "Configuración de Nginx subida"
} else {
    Write-Error "Error al subir configuración de Nginx"
    exit 1
}

# 7. Aplicar configuración en el servidor
Write-Status "7. Aplicando configuración en el servidor..."
$Commands = @(
    "cp /tmp/nginx-fixed.conf /etc/nginx/sites-available/react-app",
    "chown -R www-data:www-data $($Config.ProjectPath)",
    "chmod -R 755 $($Config.ProjectPath)",
    "nginx -t",
    "systemctl reload nginx"
)

foreach ($Command in $Commands) {
    if (Invoke-SSHCommand $Command) {
        Write-Success "Comando ejecutado: $Command"
    } else {
        Write-Error "Error ejecutando: $Command"
        if (-not $Force) {
            exit 1
        }
    }
}

# 8. Verificar estado final
Write-Status "8. Verificando estado final..."
Invoke-SSHCommand "ls -la $($Config.ProjectPath)/index.html"
Invoke-SSHCommand "ls -la $($Config.ProjectPath)/assets/ | head -10"

# Limpiar archivos temporales
Remove-Item "nginx-fixed.conf" -ErrorAction SilentlyContinue

Write-Success "Correccion de problemas completada!"
Write-Status "Puedes verificar la aplicacion en: http://$($Config.VPSHost)"

Write-Status "Resumen de cambios realizados:"
Write-Host "  - Backup creado en $($Config.BackupPath)/$BackupName" -ForegroundColor Green
Write-Host "  - Archivos de build actualizados" -ForegroundColor Green
Write-Host "  - Configuracion de Nginx corregida" -ForegroundColor Green
Write-Host "  - Permisos de archivos corregidos" -ForegroundColor Green
Write-Host "  - Servicios reiniciados" -ForegroundColor Green
