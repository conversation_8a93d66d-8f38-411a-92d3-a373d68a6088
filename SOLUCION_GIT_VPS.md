# Solución: Error "fatal: not a git repository" en VPS

## Problema Identificado
El directorio `/var/www/html/Reac_fuse_v2` en el VPS no es un repositorio Git válido, por lo que no se puede ejecutar `git pull`.

## An<PERSON><PERSON><PERSON> del <PERSON>rror
```bash
root@www:/var/www/html/Reac_fuse_v2# git pull origin dev_database
fatal: not a git repository (or any of the parent directories): .git
```

Este error indica que:
1. El directorio no contiene una carpeta `.git`
2. El proyecto no fue clonado correctamente
3. El repositorio fue corrompido o eliminado

## 🔑 Autenticación con GitHub API Token

**⚠️ IMPORTANTE**: GitHub ya no permite autenticación con usuario/contraseña desde agosto 2021. **DEBES usar un API token**.

### Formato de Clonado con API Token:
```bash
git clone https://<EMAIL>/usuario/repositorio.git
```

### Para tu repositorio específico:
```bash
git clone https://<EMAIL>/nicomagno17/Reac_fuse_v2.git
```

### ¿Por qué necesitas API token?
- GitHub eliminó la autenticación con contraseña por seguridad
- Los API tokens son más seguros y tienen permisos específicos
- Evita que te pida usuario/contraseña repetidamente

## Solución Paso a Paso

### Paso 1: Conectar al VPS
```bash
ssh -i ~/.ssh/id_ed25519_server -p 22222 root@**************
```

### Paso 2: Diagnosticar el Problema
```bash
# Navegar al directorio web
cd /var/www/html

# Verificar contenido
ls -la

# Verificar si existe el directorio del proyecto
cd Reac_fuse_v2 2>/dev/null || echo "Directorio no existe"

# Verificar si es un repositorio git válido
ls -la .git 2>/dev/null || echo "No es un repositorio git"
```

### Paso 3: Reparar el Repositorio

#### Opción A: Si el directorio existe pero no es un repositorio git
```bash
cd /var/www/html
rm -rf Reac_fuse_v2
# Usar API token para autenticación
git clone https://<EMAIL>/nicomagno17/Reac_fuse_v2.git
cd Reac_fuse_v2
git checkout dev_database
git pull origin dev_database
```

#### Opción B: Si el directorio no existe
```bash
cd /var/www/html
# Usar API token para autenticación
git clone https://<EMAIL>/nicomagno17/Reac_fuse_v2.git
cd Reac_fuse_v2
git checkout dev_database
git pull origin dev_database
```

### Paso 4: Verificar la Solución
```bash
# Verificar estado del repositorio
git status

# Verificar rama actual
git branch

# Verificar ramas remotas
git branch -r

# Verificar último commit
git log --oneline -1

# Verificar configuración remota
git remote -v
```

## Comandos de Ejecución Automática

### Script Completo para Copiar y Pegar
```bash
#!/bin/bash
# Navegar al directorio web
cd /var/www/html

# Verificar y limpiar directorio existente
if [ -d "Reac_fuse_v2" ]; then
    echo "Eliminando directorio existente..."
    rm -rf Reac_fuse_v2
fi

# Clonar repositorio fresco usando API token
echo "Clonando repositorio desde GitHub con API token..."
git clone https://<EMAIL>/nicomagno17/Reac_fuse_v2.git

# Configurar repositorio
cd Reac_fuse_v2
echo "Cambiando a rama dev_database..."
git checkout dev_database
git pull origin dev_database

# Verificar estado final
echo "=== ESTADO FINAL ==="
pwd
git status
git branch
git log --oneline -1
```

## Prevención de Futuros Problemas

### 1. Verificar Repositorio Antes de Pull
```bash
# Siempre verificar que es un repositorio git válido
git status
```

### 2. Configurar Alias Útiles
```bash
# Agregar al ~/.bashrc
alias gitcheck="git status && git branch && git remote -v"
alias gitupdate="git checkout dev_database && git pull origin dev_database"
```

### 3. Script de Mantenimiento Regular
```bash
#!/bin/bash
# check_git_health.sh
cd /var/www/html/Reac_fuse_v2

if [ ! -d ".git" ]; then
    echo "ERROR: No es un repositorio git válido"
    exit 1
fi

git fetch
echo "Estado del repositorio:"
git status
echo "Diferencias con remoto:"
git diff HEAD origin/dev_database --stat
```

## Comandos de Verificación Post-Solución

```bash
# 1. Verificar estructura del proyecto
ls -la /var/www/html/Reac_fuse_v2/

# 2. Verificar configuración git
cd /var/www/html/Reac_fuse_v2
git config --list

# 3. Verificar conectividad con GitHub
git ls-remote origin

# 4. Verificar permisos
ls -la /var/www/html/Reac_fuse_v2/
```

## Información del Servidor
- **IP:** **************
- **Puerto SSH:** 22222
- **Usuario:** root
- **Clave SSH:** ~/.ssh/id_ed25519_server
- **OS:** Ubuntu 22.04.5 LTS
- **Directorio Web:** /var/www/html/
- **Proyecto:** Reac_fuse_v2
- **Rama Principal:** dev_database
- **Repositorio:** https://github.com/nicomagno17/Reac_fuse_v2
- **GitHub API Token:** ****************************************

## Notas Importantes
1. **API Token requerido**: GitHub requiere API token para clonado (ya no acepta usuario/contraseña)
2. **Formato del clone**: `git clone https://<EMAIL>/usuario/repositorio.git`
3. Siempre hacer backup antes de eliminar directorios
4. Verificar permisos después del clone
5. Confirmar que nginx/apache apunta al directorio correcto
6. Reiniciar servicios web si es necesario
7. **Seguridad**: Nunca compartir el API token públicamente

## Contacto y Soporte
- Fecha de solución: 2025-09-06
- Documentado por: Asistente IA
- Estado: Pendiente de ejecución manual