server {
    listen 80;
    listen [::]:80;
    
    # Cambia este dominio por tu dominio real o usa _ para cualquier dominio
    server_name 45.236.129.200 _;
    
    # Directorio donde están los archivos de la aplicación React
    root /var/www/html/react-app;
    index index.html;
    
    # Configuración para archivos estáticos
    location / {
        try_files $uri $uri/ /index.html;
        
        # Headers para mejorar el rendimiento
        add_header Cache-Control "public, max-age=31536000" always;
        
        # Configuración para archivos específicos
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
        }
        
        # No cachear el index.html para asegurar actualizaciones
        location = /index.html {
            add_header Cache-Control "no-cache, no-store, must-revalidate";
            add_header Pragma "no-cache";
            add_header Expires "0";
        }
    }
    
    # Configuración para API si es necesario (opcional)
    location /api/ {
        # Proxy a tu backend API si tienes uno
        # proxy_pass http://localhost:3001;
        # proxy_http_version 1.1;
        # proxy_set_header Upgrade $http_upgrade;
        # proxy_set_header Connection 'upgrade';
        # proxy_set_header Host $host;
        # proxy_cache_bypass $http_upgrade;
        # proxy_set_header X-Real-IP $remote_addr;
        # proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        # proxy_set_header X-Forwarded-Proto $scheme;
    }
    
    # Configuración de seguridad
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;
    add_header Content-Security-Policy "default-src 'self' http: https: data: blob: 'unsafe-inline'" always;
    
    # Logs
    access_log /var/log/nginx/react-app.access.log;
    error_log /var/log/nginx/react-app.error.log;
    
    # Compresión gzip
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_proxied expired no-cache no-store private must-revalidate auth;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/javascript
        application/xml+rss
        application/json;
}
