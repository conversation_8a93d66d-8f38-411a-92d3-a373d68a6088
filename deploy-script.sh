#!/bin/bash

# Script de Despliegue Automático - React Fuse Application
# Autor: Sistema de Despliegue Automatizado
# Fecha: $(date)

set -e  # Salir si cualquier comando falla

echo "=== Iniciando Despliegue de React Fuse Application ==="
echo "Servidor: **************"
echo "Fecha: $(date)"
echo ""

# Colores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Función para imprimir mensajes
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Verificar si se está ejecutando como root
if [ "$EUID" -ne 0 ]; then
    print_error "Este script debe ejecutarse como root"
    echo "Ejecuta: sudo $0"
    exit 1
fi

print_status "Verificando sistema..."

# Verificar que nginx esté instalado
if ! command -v nginx &> /dev/null; then
    print_error "Nginx no está instalado"
    exit 1
fi

print_status "Nginx encontrado: $(nginx -v 2>&1)"

# Crear directorio para la aplicación
print_status "Creando directorio para la aplicación..."
mkdir -p /var/www/html/react-app
chown -R www-data:www-data /var/www/html/react-app
chmod -R 755 /var/www/html/react-app

# Verificar si existe el archivo de despliegue
if [ ! -f "react-fuse-deployment.zip" ]; then
    print_error "No se encontró el archivo react-fuse-deployment.zip"
    print_warning "Asegúrate de que el archivo esté en el directorio actual"
    exit 1
fi

print_status "Extrayendo archivos de la aplicación..."

# Crear directorio temporal
TEMP_DIR=$(mktemp -d)
cd "$TEMP_DIR"

# Extraer el archivo zip
unzip -q "/root/react-fuse-deployment.zip" || {
    print_error "Error al extraer el archivo zip"
    exit 1
}

# Copiar archivos de la aplicación
print_status "Copiando archivos de la aplicación..."
if [ -d "build" ]; then
    cp -r build/* /var/www/html/react-app/
else
    # Si los archivos están en la raíz del zip
    find . -name "*.html" -o -name "*.js" -o -name "*.css" -o -name "*.json" -o -name "*.ico" | while read file; do
        cp "$file" /var/www/html/react-app/ 2>/dev/null || true
    done
    
    # Copiar directorio assets si existe
    if [ -d "assets" ]; then
        cp -r assets /var/www/html/react-app/
    fi
fi

# Verificar que se copiaron los archivos
if [ ! -f "/var/www/html/react-app/index.html" ]; then
    print_error "No se encontró index.html en los archivos extraídos"
    print_warning "Verificando contenido del zip..."
    ls -la
    exit 1
fi

print_status "Archivos de la aplicación copiados exitosamente"

# Configurar nginx
print_status "Configurando Nginx..."

# Respaldar configuración actual
if [ -f "/etc/nginx/sites-available/default" ]; then
    cp /etc/nginx/sites-available/default /etc/nginx/sites-available/default.backup.$(date +%Y%m%d_%H%M%S)
    print_status "Configuración actual respaldada"
fi

# Copiar nueva configuración si existe
if [ -f "nginx-react-config.conf" ]; then
    cp nginx-react-config.conf /etc/nginx/sites-available/react-app
    print_status "Nueva configuración de Nginx copiada"
else
    # Crear configuración básica si no existe
    print_warning "Creando configuración básica de Nginx..."
    cat > /etc/nginx/sites-available/react-app << 'EOF'
server {
    listen 80;
    listen [::]:80;
    server_name ************** _;
    
    root /var/www/html/react-app;
    index index.html;
    
    location / {
        try_files $uri $uri/ /index.html;
        add_header Cache-Control "public, max-age=31536000" always;
    }
    
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
    
    location = /index.html {
        add_header Cache-Control "no-cache, no-store, must-revalidate";
        add_header Pragma "no-cache";
        add_header Expires "0";
    }
    
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types text/plain text/css text/xml text/javascript application/javascript application/xml+rss application/json;
}
EOF
fi

# Habilitar el sitio
ln -sf /etc/nginx/sites-available/react-app /etc/nginx/sites-enabled/react-app

# Deshabilitar sitio por defecto
if [ -L "/etc/nginx/sites-enabled/default" ]; then
    rm /etc/nginx/sites-enabled/default
    print_status "Sitio por defecto deshabilitado"
fi

# Verificar configuración de nginx
print_status "Verificando configuración de Nginx..."
if nginx -t; then
    print_status "Configuración de Nginx válida"
else
    print_error "Error en la configuración de Nginx"
    exit 1
fi

# Reiniciar nginx
print_status "Reiniciando Nginx..."
systemctl restart nginx

if systemctl is-active --quiet nginx; then
    print_status "Nginx reiniciado exitosamente"
else
    print_error "Error al reiniciar Nginx"
    systemctl status nginx
    exit 1
fi

# Verificar que nginx esté escuchando en puerto 80
if netstat -tlnp | grep -q ":80 "; then
    print_status "Nginx está escuchando en puerto 80"
else
    print_warning "Nginx podría no estar escuchando en puerto 80"
fi

# Limpiar archivos temporales
cd /
rm -rf "$TEMP_DIR"

print_status "Limpieza completada"

echo ""
echo "=== Despliegue Completado Exitosamente ==="
echo -e "${GREEN}✓${NC} Aplicación React desplegada en: http://**************"
echo -e "${GREEN}✓${NC} Archivos ubicados en: /var/www/html/react-app"
echo -e "${GREEN}✓${NC} Configuración de Nginx: /etc/nginx/sites-available/react-app"
echo ""
echo "Para verificar el funcionamiento:"
echo "1. Abrir navegador en: http://**************"
echo "2. Verificar logs: sudo tail -f /var/log/nginx/access.log"
echo "3. Verificar errores: sudo tail -f /var/log/nginx/error.log"
echo ""
echo "=== Fin del Despliegue ==="
