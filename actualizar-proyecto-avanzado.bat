@echo off
setlocal enabledelayedexpansion

echo ========================================
echo    ACTUALIZADOR AVANZADO REACT FUSE
echo ========================================
echo.

REM Configuración de variables
set SSH_KEY=%USERPROFILE%\.ssh\id_ed25519_server
set SSH_PORT=22222
set SSH_USER=root
set SSH_HOST=**************
set PROJECT_PATH=/var/www/html/Reac_fuse_v2
set BACKUP_PATH=/var/www/html/backups

REM Mostrar menú de opciones
echo Selecciona una opción:
echo.
echo 1. Actualización rápida (pull de rama actual)
echo 2. Actualización con rebuild (npm install + build)
echo 3. Cambiar a rama específica
echo 4. Ver estado del repositorio
echo 5. Restaurar desde backup
echo 6. Limpiar backups antiguos
echo 7. Salir
echo.
set /p OPCION="Ingresa tu opción (1-7): "

if "%OPCION%"=="1" goto :actualizacion_rapida
if "%OPCION%"=="2" goto :actualizacion_rebuild
if "%OPCION%"=="3" goto :cambiar_rama
if "%OPCION%"=="4" goto :ver_estado
if "%OPCION%"=="5" goto :restaurar_backup
if "%OPCION%"=="6" goto :limpiar_backups
if "%OPCION%"=="7" goto :salir
goto :opcion_invalida

:actualizacion_rapida
echo.
echo [INFO] Ejecutando actualización rápida...
call :ejecutar_ssh "
cd %PROJECT_PATH%
echo '[SERVIDOR] Creando backup...'
mkdir -p %BACKUP_PATH%
BACKUP_NAME=backup_$(date +%%Y%%m%%d_%%H%%M%%S)
cp -r %PROJECT_PATH% %BACKUP_PATH%/\$BACKUP_NAME
echo '[SERVIDOR] Haciendo pull...'
git pull origin \$(git branch --show-current)
chmod -R 755 %PROJECT_PATH%/assets/
systemctl reload nginx
echo '[SERVIDOR] ✅ Actualización rápida completada'
"
goto :fin

:actualizacion_rebuild
echo.
echo [INFO] Ejecutando actualización con rebuild...
echo [ADVERTENCIA] Esto puede tomar varios minutos...
set /p CONFIRMAR="¿Continuar? (s/n): "
if /i not "%CONFIRMAR%"=="s" goto :fin

call :ejecutar_ssh "
cd %PROJECT_PATH%
echo '[SERVIDOR] Creando backup...'
mkdir -p %BACKUP_PATH%
BACKUP_NAME=backup_rebuild_$(date +%%Y%%m%%d_%%H%%M%%S)
cp -r %PROJECT_PATH% %BACKUP_PATH%/\$BACKUP_NAME
echo '[SERVIDOR] Haciendo pull...'
git pull origin \$(git branch --show-current)
echo '[SERVIDOR] Instalando dependencias...'
npm install
echo '[SERVIDOR] Construyendo aplicación...'
npm run build
chmod -R 755 %PROJECT_PATH%/assets/
systemctl reload nginx
echo '[SERVIDOR] ✅ Actualización con rebuild completada'
"
goto :fin

:cambiar_rama
echo.
set /p RAMA="Ingresa el nombre de la rama: "
if "%RAMA%"=="" (
    echo [ERROR] Nombre de rama no puede estar vacío
    goto :fin
)

call :ejecutar_ssh "
cd %PROJECT_PATH%
echo '[SERVIDOR] Creando backup antes de cambiar rama...'
mkdir -p %BACKUP_PATH%
BACKUP_NAME=backup_branch_$(date +%%Y%%m%%d_%%H%%M%%S)
cp -r %PROJECT_PATH% %BACKUP_PATH%/\$BACKUP_NAME
echo '[SERVIDOR] Obteniendo ramas remotas...'
git fetch origin
echo '[SERVIDOR] Cambiando a rama: %RAMA%'
git checkout %RAMA%
git pull origin %RAMA%
chmod -R 755 %PROJECT_PATH%/assets/
systemctl reload nginx
echo '[SERVIDOR] ✅ Cambio de rama completado'
"
goto :fin

:ver_estado
echo.
echo [INFO] Consultando estado del repositorio...
call :ejecutar_ssh "
cd %PROJECT_PATH%
echo '[SERVIDOR] Estado del repositorio:'
echo '=================================='
git status
echo ''
echo '[SERVIDOR] Rama actual:'
git branch --show-current
echo ''
echo '[SERVIDOR] Últimos 3 commits:'
git log --oneline -3
echo ''
echo '[SERVIDOR] Archivos modificados:'
git diff --name-only
"
goto :fin

:restaurar_backup
echo.
echo [INFO] Listando backups disponibles...
call :ejecutar_ssh "
cd %BACKUP_PATH%
echo '[SERVIDOR] Backups disponibles:'
ls -la | grep backup
"
echo.
set /p BACKUP_NAME="Ingresa el nombre del backup a restaurar: "
if "%BACKUP_NAME%"=="" (
    echo [ERROR] Nombre de backup no puede estar vacío
    goto :fin
)

set /p CONFIRMAR="¿Estás seguro de restaurar el backup '%BACKUP_NAME%'? (s/n): "
if /i not "%CONFIRMAR%"=="s" goto :fin

call :ejecutar_ssh "
cd %BACKUP_PATH%
if [ -d '%BACKUP_NAME%' ]; then
    echo '[SERVIDOR] Restaurando backup: %BACKUP_NAME%'
    rm -rf %PROJECT_PATH%
    cp -r %BACKUP_PATH%/%BACKUP_NAME% %PROJECT_PATH%
    chmod -R 755 %PROJECT_PATH%/assets/
    systemctl reload nginx
    echo '[SERVIDOR] ✅ Backup restaurado exitosamente'
else
    echo '[SERVIDOR] ❌ Backup no encontrado: %BACKUP_NAME%'
fi
"
goto :fin

:limpiar_backups
echo.
echo [ADVERTENCIA] Esto eliminará backups antiguos (más de 7 días)
set /p CONFIRMAR="¿Continuar? (s/n): "
if /i not "%CONFIRMAR%"=="s" goto :fin

call :ejecutar_ssh "
cd %BACKUP_PATH%
echo '[SERVIDOR] Eliminando backups antiguos...'
find . -name 'backup_*' -type d -mtime +7 -exec rm -rf {} +
echo '[SERVIDOR] ✅ Limpieza de backups completada'
echo '[SERVIDOR] Backups restantes:'
ls -la | grep backup
"
goto :fin

:opcion_invalida
echo.
echo [ERROR] Opción inválida. Por favor selecciona una opción del 1 al 7.
pause
goto :fin

:salir
echo.
echo Saliendo...
goto :fin

REM Función para ejecutar comandos SSH
:ejecutar_ssh
if not exist "%SSH_KEY%" (
    echo [ERROR] No se encontró la clave SSH en: %SSH_KEY%
    exit /b 1
)

ssh -i "%SSH_KEY%" -p %SSH_PORT% %SSH_USER%@%SSH_HOST% %~1

if %ERRORLEVEL% neq 0 (
    echo [ERROR] Error al ejecutar comando en el servidor
    exit /b 1
)
exit /b 0

:fin
echo.
echo Presiona cualquier tecla para continuar...
pause >nul
