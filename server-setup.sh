#!/bin/bash

# Script de configuración para el servidor VPS
# Este script instala y configura todo lo necesario para React Fuse v2

set -e  # Salir si hay algún error

echo "=== CONFIGURACIÓN DEL SERVIDOR VPS PARA REACT FUSE V2 ==="
echo "Iniciando configuración..."
echo ""

# Función para verificar si un comando existe
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Actualizar el sistema
echo "1. Actualizando el sistema..."
apt update && apt upgrade -y

# Instalar dependencias básicas
echo "2. Instalando dependencias básicas..."
apt install -y curl wget git build-essential

# Instalar Node.js 22.x (versión compatible)
echo "3. Instalando Node.js 22.x..."
if ! command_exists node; then
    curl -fsSL https://deb.nodesource.com/setup_22.x | bash -
    apt-get install -y nodejs
else
    echo "Node.js ya está instalado: $(node --version)"
fi

# Verificar versiones
echo "4. Verificando versiones instaladas..."
echo "Node.js: $(node --version)"
echo "npm: $(npm --version)"
echo "Git: $(git --version)"

# Configurar npm para evitar problemas de permisos
echo "5. Configurando npm..."
npm config set fund false
npm config set audit-level moderate

# Navegar al directorio del proyecto
echo "6. Navegando al directorio del proyecto..."
if [ -d "Reac_fuse_v2" ]; then
    cd Reac_fuse_v2
    echo "Directorio del proyecto encontrado"
else
    echo "❌ Error: Directorio Reac_fuse_v2 no encontrado"
    echo "Asegúrate de ejecutar este script desde el directorio que contiene Reac_fuse_v2"
    exit 1
fi

# Limpiar instalación anterior si existe
echo "7. Limpiando instalación anterior..."
if [ -d "node_modules" ]; then
    echo "Eliminando node_modules existente..."
    rm -rf node_modules
fi

if [ -f "package-lock.json" ]; then
    echo "Eliminando package-lock.json existente..."
    rm -f package-lock.json
fi

# Limpiar cache de npm
echo "8. Limpiando cache de npm..."
npm cache clean --force

# Instalar dependencias
echo "9. Instalando dependencias del proyecto..."
npm install

# Verificar instalación
echo "10. Verificando instalación..."
if [ -d "node_modules" ]; then
    echo "✅ node_modules instalado correctamente"
    echo "Tamaño: $(du -sh node_modules)"
else
    echo "❌ Error: node_modules no se instaló correctamente"
    exit 1
fi

# Crear archivo de variables de entorno para producción
echo "11. Creando archivo de configuración de producción..."
cat > .env.production << EOF
# Configuración de producción para React Fuse v2
VITE_API_BASE_URL=http://**************:3000
VITE_PORT=3000

# Database Configuration
VITE_DB_HOST=**************
VITE_DB_PORT=3306
VITE_DB_USER=ncornejo
VITE_DB_PASSWORD=N1c0l7as17#
VITE_DB_NAME=renovatio
EOF

echo "✅ Archivo .env.production creado"

# Intentar compilar el proyecto
echo "12. Intentando compilar el proyecto..."
if npm run build; then
    echo "✅ Compilación exitosa"
else
    echo "❌ Error en la compilación"
    echo "Revisando errores..."
fi

echo ""
echo "=== CONFIGURACIÓN COMPLETADA ==="
echo "Para ejecutar el proyecto:"
echo "1. Desarrollo: npm run dev"
echo "2. Producción: npm run build && npm run preview"
echo ""
echo "El proyecto debería estar disponible en:"
echo "- Desarrollo: http://**************:3000"
echo "- Producción: http://**************:4173"
echo ""
