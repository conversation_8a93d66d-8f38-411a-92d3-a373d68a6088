import { useQuery } from '@tanstack/react-query';
import { httpApiService } from '../services/httpApiService';

/**
 * Hook to test API connection
 */
export const useTestDatabaseConnection = () => {
	return useQuery<boolean>({
		queryKey: ['kpi', 'api', 'connection'],
		queryFn: () => httpApiService.testConnection(),
		staleTime: 2 * 60 * 1000, // 2 minutes
		refetchOnWindowFocus: false,
		retry: 2
	});
};
