import { useQuery } from '@tanstack/react-query';
import { httpApiService } from '../services/httpApiService';
import type { UserStats } from '../types';

/**
 * Hook to get user statistics from API
 */
export function useGetUserStats() {
	return useQuery<UserStats>({
		queryKey: ['kpi', 'userStats'],
		queryFn: () => httpApiService.getUserStats(),
		staleTime: 30000, // Consider data stale after 30 seconds
		refetchInterval: 60000, // Refetch every minute
		retry: 3,
		retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000)
	});
}