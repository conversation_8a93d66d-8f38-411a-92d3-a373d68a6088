#!/bin/bash

# Script de diagnóstico para el servidor VPS
# Ejecutar este script en el servidor para diagnosticar problemas

echo "=== DIAGNÓSTICO DEL SERVIDOR VPS ==="
echo "Fecha: $(date)"
echo "Usuario: $(whoami)"
echo "Directorio actual: $(pwd)"
echo ""

echo "=== INFORMACIÓN DEL SISTEMA ==="
echo "Sistema operativo:"
cat /etc/os-release | head -5
echo ""
echo "Arquitectura: $(uname -m)"
echo "Memoria disponible:"
free -h
echo ""
echo "Espacio en disco:"
df -h
echo ""

echo "=== VERSIONES DE SOFTWARE ==="
echo "Node.js version:"
node --version 2>/dev/null || echo "Node.js no está instalado"
echo ""
echo "npm version:"
npm --version 2>/dev/null || echo "npm no está instalado"
echo ""
echo "Git version:"
git --version 2>/dev/null || echo "Git no está instalado"
echo ""

echo "=== ESTADO DEL PROYECTO ==="
if [ -d "Reac_fuse_v2" ]; then
    echo "Directorio del proyecto encontrado: Reac_fuse_v2"
    cd Reac_fuse_v2
    echo "Contenido del directorio:"
    ls -la
    echo ""
    
    if [ -f "package.json" ]; then
        echo "package.json encontrado"
        echo "Versión del proyecto:"
        grep '"version"' package.json
        echo ""
        echo "Engines requeridos:"
        grep -A 3 '"engines"' package.json
        echo ""
    else
        echo "❌ package.json NO encontrado"
    fi
    
    if [ -d "node_modules" ]; then
        echo "✅ node_modules existe"
        echo "Tamaño de node_modules:"
        du -sh node_modules
    else
        echo "❌ node_modules NO existe"
    fi
    
    if [ -f "package-lock.json" ]; then
        echo "✅ package-lock.json existe"
    else
        echo "❌ package-lock.json NO existe"
    fi
    
else
    echo "❌ Directorio del proyecto Reac_fuse_v2 NO encontrado"
    echo "Contenido del directorio actual:"
    ls -la
fi

echo ""
echo "=== PUERTOS EN USO ==="
echo "Puertos 3000-3010 en uso:"
netstat -tlnp | grep -E ':(300[0-9]|301[0])'
echo ""

echo "=== PROCESOS NODE.JS ==="
ps aux | grep node | grep -v grep
echo ""

echo "=== VARIABLES DE ENTORNO RELEVANTES ==="
echo "PATH: $PATH"
echo "NODE_ENV: $NODE_ENV"
echo "HOME: $HOME"
echo ""

echo "=== FIN DEL DIAGNÓSTICO ==="
