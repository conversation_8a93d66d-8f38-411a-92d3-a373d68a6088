#!/bin/bash
# Script para ACTUALIZACIONES NORMALES del proyecto en VPS
# Fecha: 2025-09-06
# USO: ./actualizar_vps.sh
# PROPÓSITO: Actualizar el código del proyecto SIN re-clonar

echo "=== ACTUALIZACIÓN NORMAL DEL PROYECTO ==="
echo "📦 Actualizando rama dev_database..."

# Navegar al directorio del proyecto
cd /var/www/html/Reac_fuse_v2

# Verificar que estamos en un repositorio git válido
if [ ! -d ".git" ]; then
    echo "❌ ERROR: No es un repositorio git válido"
    echo "💡 Usa el script fix_git_vps.sh para reparar"
    exit 1
fi

# Verificar estado actual
echo "📊 Estado actual del repositorio:"
git status

# Verificar rama actual
current_branch=$(git branch --show-current)
echo "🌳 Rama actual: $current_branch"

# Cambiar a dev_database si no estamos en ella
if [ "$current_branch" != "dev_database" ]; then
    echo "🔄 Cambiando a rama dev_database..."
    git checkout dev_database
fi

# Hacer fetch para ver cambios disponibles
echo "🔍 Verificando cambios disponibles..."
git fetch origin dev_database

# Mostrar diferencias si las hay
if ! git diff --quiet HEAD origin/dev_database; then
    echo "📋 Cambios disponibles para descargar:"
    git log --oneline HEAD..origin/dev_database
else
    echo "✅ El proyecto ya está actualizado"
    exit 0
fi

# Hacer pull de los cambios
echo "⬇️  Descargando cambios..."
if git pull origin dev_database; then
    echo "✅ ¡Actualización completada exitosamente!"
    
    # Verificar estado final
    echo "📊 Estado final:"
    git log --oneline -3
    
    # Verificar si hay archivos de build que necesiten recompilación
    if [ -f "package.json" ]; then
        echo "💡 NOTA: Si modificaste dependencias, ejecuta:"
        echo "   npm install"
        echo "   npm run build"
    fi
    
else
    echo "❌ ERROR durante la actualización"
    echo "💡 Posibles conflictos. Revisa con:"
    echo "   git status"
    echo "   git log"
    exit 1
fi

echo "🎉 ¡Listo! Proyecto actualizado correctamente."