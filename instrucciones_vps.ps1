Write-Host "=== SOLUCION PARA ERROR DE GIT EN VPS ===" -ForegroundColor Green
Write-Host ""
Write-Host "PROBLEMA DETECTADO:" -ForegroundColor Red
Write-Host "El directorio /var/www/html/Reac_fuse_v2 no es un repositorio Git valido" -ForegroundColor Yellow
Write-Host ""
Write-Host "PASOS PARA SOLUCIONAR:" -ForegroundColor Green
Write-Host ""
Write-Host "1. Conecta al VPS:" -ForegroundColor Cyan
Write-Host "   ssh -i ~/.ssh/id_ed25519_server -p 22222 root@**************" -ForegroundColor White
Write-Host ""
Write-Host "2. Ejecuta estos comandos en el VPS:" -ForegroundColor Cyan
Write-Host ""
Write-Host "cd /var/www/html" -ForegroundColor White
Write-Host "rm -rf Reac_fuse_v2" -ForegroundColor White
Write-Host "git clone https://github.com/nicomagno17/Reac_fuse_v2.git" -ForegroundColor White
Write-Host "cd Reac_fuse_v2" -ForegroundColor White
Write-Host "git checkout dev_database" -ForegroundColor White
Write-Host "git pull origin dev_database" -ForegroundColor White
Write-Host ""
Write-Host "3. Verifica que funcione:" -ForegroundColor Cyan
Write-Host "git status" -ForegroundColor White
Write-Host "git branch" -ForegroundColor White
Write-Host ""
Write-Host "LISTO! El problema deberia estar solucionado." -ForegroundColor Green