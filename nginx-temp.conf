server {
    listen 80;
    listen [::]:80;
    
    server_name 45.236.129.200 _;
    
    root /var/www/html;
    index index.html;
    
    location / {
        try_files $uri $uri/ /index.html;
        add_header Cache-Control "no-cache, no-store, must-revalidate" always;
        add_header Pragma "no-cache" always;
        add_header Expires "0" always;
    }

    location /apps/ {
        try_files $uri $uri/ /Reac_fuse_v2/build/index.html;
        root /var/www/html;
        add_header Cache-Control "no-cache, no-store, must-revalidate" always;
        add_header Pragma "no-cache" always;
        add_header Expires "0" always;
    }

    location /assets/ {
        try_files $uri $uri/ =404;
        root /var/www/html/Reac_fuse_v2/build;
        add_header Cache-Control "no-cache, no-store, must-revalidate" always;
        add_header Pragma "no-cache" always;
        add_header Expires "0" always;
    }
}
