#!/bin/bash
# Script para configurar Nginx para aplicación React/Vite en VPS
# Fecha: 2025-09-06
# Uso: Ejecutar en el VPS después de SSH

echo "=== CONFIGURACIÓN DE NGINX PARA REACT/VITE ==="

# Verificar si Nginx está instalado
if ! command -v nginx &> /dev/null; then
    echo "❌ Nginx no está instalado. Instalando..."
    apt update
    apt install -y nginx
else
    echo "✅ Nginx está instalado"
fi

# Backup de configuración actual
echo "📋 Creando backup de configuración actual..."
cp /etc/nginx/sites-available/default /etc/nginx/sites-available/default.backup.$(date +%Y%m%d_%H%M%S)

# Crear configuración optimizada para React
echo "⚙️ Creando configuración optimizada para React/Vite..."

cat > /etc/nginx/sites-available/react-app << 'EOF'
server {
    listen 80;
    server_name **************;  # IP del VPS
    
    # Directorio raíz donde está tu aplicación React
    root /var/www/html/Reac_fuse_v2/dist;
    index index.html;
    
    # Configuración de MIME types para archivos JavaScript y CSS
    location ~* \.(js)$ {
        add_header Content-Type application/javascript;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
    
    location ~* \.(css)$ {
        add_header Content-Type text/css;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
    
    location ~* \.(woff|woff2|ttf|eot|otf)$ {
        add_header Content-Type font/woff;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
    
    location ~* \.(png|jpg|jpeg|gif|ico|svg)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
    
    # Configuración para archivos estáticos de assets
    location /assets/ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        
        # MIME types específicos para assets
        location ~* /assets/.*\.js$ {
            add_header Content-Type application/javascript;
        }
        
        location ~* /assets/.*\.css$ {
            add_header Content-Type text/css;
        }
    }
    
    # Configuración principal para SPA - todas las rutas van a index.html
    location / {
        try_files $uri $uri/ /index.html;
        
        # Headers de seguridad
        add_header X-Frame-Options DENY;
        add_header X-Content-Type-Options nosniff;
        add_header X-XSS-Protection "1; mode=block";
    }
    
    # Configuración para index.html (no cachear)
    location = /index.html {
        add_header Cache-Control "no-cache, no-store, must-revalidate";
        add_header Pragma "no-cache";
        add_header Expires "0";
    }
    
    # Habilitar compresión
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/javascript
        application/xml+rss
        application/json;
    
    # Configuración de logs
    access_log /var/log/nginx/react_app_access.log;
    error_log /var/log/nginx/react_app_error.log;
}
EOF

# Verificar que el directorio del proyecto existe
if [ ! -d "/var/www/html/Reac_fuse_v2" ]; then
    echo "⚠️ ADVERTENCIA: El directorio /var/www/html/Reac_fuse_v2 no existe"
    echo "💡 Ejecuta primero el script de clonado del repositorio"
fi

# Verificar que el directorio dist existe
if [ ! -d "/var/www/html/Reac_fuse_v2/dist" ]; then
    echo "⚠️ ADVERTENCIA: El directorio /var/www/html/Reac_fuse_v2/dist no existe"
    echo "💡 Necesitas compilar el proyecto React con: npm run build"
fi

# Desactivar sitio por defecto
echo "🔧 Desactivando sitio por defecto..."
rm -f /etc/nginx/sites-enabled/default

# Activar nueva configuración
echo "🔧 Activando nueva configuración..."
ln -sf /etc/nginx/sites-available/react-app /etc/nginx/sites-enabled/

# Verificar configuración
echo "🔍 Verificando configuración de Nginx..."
nginx -t

if [ $? -eq 0 ]; then
    echo "✅ Configuración válida"
    
    # Reiniciar Nginx
    echo "🔄 Reiniciando Nginx..."
    systemctl restart nginx
    systemctl enable nginx
    
    echo "📊 Estado de Nginx:"
    systemctl status nginx --no-pager
    
    echo ""
    echo "🎉 ¡Configuración completada!"
    echo ""
    echo "📋 PRÓXIMOS PASOS:"
    echo "1. Asegúrate de que el proyecto esté clonado en /var/www/html/Reac_fuse_v2"
    echo "2. Compila el proyecto: cd /var/www/html/Reac_fuse_v2 && npm run build"
    echo "3. Verifica permisos: chown -R www-data:www-data /var/www/html/Reac_fuse_v2"
    echo "4. Accede a: http://**************"
    
else
    echo "❌ Error en configuración de Nginx"
    echo "Revisa los logs: nginx -t"
    exit 1
fi

echo ""
echo "📝 VERIFICACIONES ÚTILES:"
echo "- Ver logs de error: tail -f /var/log/nginx/react_app_error.log"
echo "- Ver logs de acceso: tail -f /var/log/nginx/react_app_access.log"
echo "- Recargar configuración: nginx -s reload"
echo "- Estado del servicio: systemctl status nginx"