import { useQuery } from '@tanstack/react-query';
import { httpApiService } from '../services/httpApiService';
import type { KpiNotification } from '../types';

/**
 * Hook to get all KPI notifications from the API
 */
export const useGetAllKpiNotifications = () => {
	return useQuery<KpiNotification[]>({
		queryKey: ['kpi', 'notifications'],
		queryFn: () => httpApiService.getNotifications(),
		staleTime: 5 * 60 * 1000, // 5 minutes
		refetchInterval: 30 * 1000, // Refetch every 30 seconds
		refetchOnWindowFocus: true,
		retry: 3,
		retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000)
	});
};
