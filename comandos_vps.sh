#!/bin/bash
# Comandos para solucionar el problema de Git en el VPS
# Copiar y pegar estos comandos uno por uno en la terminal SSH
# ACTUALIZADO: Incluye API token para autenticación con GitHub

# Paso 1: Navegar al directorio web
cd /var/www/html

# Paso 2: Eliminar el directorio corrupto (si existe)
rm -rf Reac_fuse_v2

# Paso 3: Clonar el repositorio fresco desde GitHub usando API token
echo "Clonando repositorio con API token..."
git clone https://<EMAIL>/nicomagno17/Reac_fuse_v2.git

# Paso 4: Entrar al directorio del proyecto
cd Reac_fuse_v2

# Paso 5: Cambiar a la rama dev_database
git checkout dev_database

# Paso 6: Hacer pull de los últimos cambios
git pull origin dev_database

# Paso 7: Verificar que todo esté bien
echo "=== VERIFICACION FINAL ==="
pwd
git status
git branch
git log --oneline -1

echo "🎉 ¡Listo! Repositorio clonado y configurado correctamente."