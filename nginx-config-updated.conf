server {
    listen 80;
    listen [::]:80;

    server_name ************** _;

    # Directorio donde están los archivos de la aplicación React
    root /var/www/html/Reac_fuse_v2;
    index index.html;

    # Incluir tipos MIME
    include /etc/nginx/mime.types;

    # Configuración para archivos estáticos
    location / {
        try_files $uri $uri/ /index.html;
    }

    # Configuración específica para archivos JavaScript
    location ~* \.js$ {
        add_header Content-Type application/javascript;
        expires 1y;
        add_header Cache-Control "public, immutable";
        access_log off;
    }

    # Configuración para archivos CSS
    location ~* \.css$ {
        add_header Content-Type text/css;
        expires 1y;
        add_header Cache-Control "public, immutable";
        access_log off;
    }

    # Configuración para otros archivos estáticos
    location ~* \.(png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot|webp)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        access_log off;
    }

    # No cachear el index.html para asegurar actualizaciones
    location = /index.html {
        add_header Cache-Control "no-cache, no-store, must-revalidate";
        add_header Pragma "no-cache";
        add_header Expires "0";
    }

    # Configuración de seguridad
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;
    add_header Content-Security-Policy "default-src 'self' http: https: data: blob: 'unsafe-inline' 'unsafe-eval'" always;

    # Logs
    access_log /var/log/nginx/react-fuse.access.log;
    error_log /var/log/nginx/react-fuse.error.log;

    # Compresión gzip
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_proxied expired no-cache no-store private auth;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/javascript
        application/xml+rss
        application/json
        application/x-javascript;
}