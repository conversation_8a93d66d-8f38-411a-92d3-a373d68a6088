import Card from '@mui/material/Card';
import CardContent from '@mui/material/CardContent';
import Typography from '@mui/material/Typography';
import Chip from '@mui/material/Chip';
import Box from '@mui/material/Box';
import { format } from 'date-fns';
import { es } from 'date-fns/locale';
import type { KpiNotification } from '../../api/types';

interface KpiNotificationCardProps {
	notification: KpiNotification;
	className?: string;
}

/**
 * KPI Notification Card Component
 */
function KpiNotificationCard({ notification, className = '' }: KpiNotificationCardProps) {
	const getTypeColor = (type: string) => {
		switch (type) {
			case 'success':
				return 'success';
			case 'warning':
				return 'warning';
			case 'error':
				return 'error';
			case 'info':
			default:
				return 'info';
		}
	};

	const getTypeLabel = (type: string) => {
		switch (type) {
			case 'success':
				return 'Éxito';
			case 'warning':
				return 'Advertencia';
			case 'error':
				return 'Error';
			case 'info':
			default:
				return 'Información';
		}
	};

	const formatDate = (dateString: string) => {
		try {
			const date = new Date(dateString);
			return format(date, 'dd/MM/yyyy HH:mm', { locale: es });
		} catch (error) {
			return 'Fecha inválida';
		}
	};

	return (
		<Card 
			className={`${className} mb-16 shadow-md hover:shadow-lg transition-shadow duration-200`}
			sx={{ 
				borderLeft: `4px solid`,
				borderLeftColor: (theme) => {
					const color = getTypeColor(notification.type);
					const paletteColor = theme.palette[color as keyof typeof theme.palette];
					return (paletteColor && typeof paletteColor === 'object' && 'main' in paletteColor) 
						? paletteColor.main 
						: theme.palette.info.main;
				}
			}}
		>
			<CardContent className="p-16">
				<Box className="flex justify-between items-start mb-12">
					<Typography 
						variant="h6" 
						className="font-semibold text-16 mb-8"
						color="text.primary"
					>
						{notification.title}
					</Typography>
					<Chip
						label={getTypeLabel(notification.type)}
						color={getTypeColor(notification.type) as any}
						size="small"
						variant="outlined"
					/>
				</Box>

				<Typography 
					variant="body2" 
					className="text-14 mb-12"
					color="text.secondary"
				>
					{notification.message}
				</Typography>

				<Box className="flex justify-between items-center">
					<Typography 
						variant="caption" 
						className="text-12"
						color="text.secondary"
					>
						{formatDate(notification.time || notification.created_at || '')}
					</Typography>
					
					<Box className="flex items-center space-x-8">
						{notification.read ? (
							<Chip
								label="Leído"
								size="small"
								variant="outlined"
								color="default"
							/>
						) : (
							<Chip
								label="No leído"
								size="small"
								color="primary"
								variant="filled"
							/>
						)}
						
						<Typography 
							variant="caption" 
							className="text-10"
							color="text.disabled"
						>
							ID: {notification.id}
						</Typography>
					</Box>
				</Box>
			</CardContent>
		</Card>
	);
}

export default KpiNotificationCard;
