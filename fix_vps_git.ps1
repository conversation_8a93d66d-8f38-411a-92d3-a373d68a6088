# Script para solucionar el problema de Git en el VPS
# Fecha: 2025-09-06
# Propósito: Mostrar instrucciones para conectar al VPS y configurar el repositorio Git

Write-Host "=== SCRIPT DE REPARACIÓN GIT EN VPS ===" -ForegroundColor Green
Write-Host "Problema: El directorio en el VPS no es un repositorio Git válido" -ForegroundColor Red

Write-Host "`n=== INSTRUCCIONES PASO A PASO ===" -ForegroundColor Green

Write-Host "1. Abre una nueva terminal de PowerShell" -ForegroundColor Yellow
Write-Host "2. Conecta al VPS con este comando:" -ForegroundColor Yellow
Write-Host "   ssh -i ~/.ssh/id_ed25519_server -p 22222 root@**************" -ForegroundColor White

Write-Host "`n3. Una vez conectado al VPS, ejecuta estos comandos:" -ForegroundColor Yellow

$comandosBash = @'
# Navegar al directorio web
cd /var/www/html

# Verificar estado actual
echo "=== Estado actual ==="
ls -la

# Si existe el directorio, eliminarlo (está corrupto)
if [ -d "Reac_fuse_v2" ]; then
    echo "Eliminando directorio corrupto..."
    rm -rf Reac_fuse_v2
fi

# Clonar repositorio fresco
echo "Clonando repositorio desde GitHub..."
git clone https://github.com/nicomagno17/Reac_fuse_v2.git

# Configurar repositorio
cd Reac_fuse_v2
echo "Cambiando a rama dev_database..."
git checkout dev_database
git pull origin dev_database

# Verificar estado final
echo "=== Estado final ==="
pwd
git status
git branch
'@

Write-Host $comandosBash -ForegroundColor Cyan

Write-Host "`n=== RESULTADO ESPERADO ===" -ForegroundColor Green
Write-Host "Después de ejecutar estos comandos deberías ver:" -ForegroundColor Yellow
Write-Host "- El repositorio clonado correctamente" -ForegroundColor White
Write-Host "- Rama dev_database activa" -ForegroundColor White
Write-Host "- Sin errores de git" -ForegroundColor White

Write-Host "`n=== COMANDOS FUTUROS ===" -ForegroundColor Green
Write-Host "Para futuros pulls, usa:" -ForegroundColor Yellow
Write-Host "cd /var/www/html/Reac_fuse_v2" -ForegroundColor White
Write-Host "git pull origin dev_database" -ForegroundColor White