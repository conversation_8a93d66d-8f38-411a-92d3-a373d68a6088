@echo off
echo ========================================
echo    ACTUALIZADOR SIMPLE REACT FUSE
echo ========================================
echo.

REM Configuración
set SSH_KEY=%USERPROFILE%\.ssh\id_ed25519_server
set SSH_PARAMS=-i "%SSH_KEY%" -p 22222 root@45.236.129.200

echo [INFO] Verificando clave SSH...
if not exist "%SSH_KEY%" (
    echo [ERROR] No se encontró la clave SSH en: %SSH_KEY%
    pause
    exit /b 1
)

echo [INFO] Conectando al servidor...
echo.

REM Crear backup
echo [PASO 1] Creando backup...
ssh %SSH_PARAMS% "cd /var/www/html/Reac_fuse_v2 && mkdir -p /var/www/html/backups && cp -r /var/www/html/Reac_fuse_v2 /var/www/html/backups/backup_$(date +%%Y%%m%%d_%%H%%M%%S) && echo 'Backup creado exitosamente'"

if %ERRORLEVEL% neq 0 (
    echo [ERROR] Fallo al crear backup
    pause
    exit /b 1
)

REM Hacer pull
echo.
echo [PASO 2] Actualizando código...
ssh %SSH_PARAMS% "cd /var/www/html/Reac_fuse_v2 && git pull origin $(git branch --show-current)"

if %ERRORLEVEL% neq 0 (
    echo [ERROR] Fallo al hacer pull
    pause
    exit /b 1
)

REM Ajustar permisos
echo.
echo [PASO 3] Ajustando permisos...
ssh %SSH_PARAMS% "chmod -R 755 /var/www/html/Reac_fuse_v2/assets/ && chown -R www-data:www-data /var/www/html/Reac_fuse_v2"

REM Reiniciar Nginx
echo.
echo [PASO 4] Reiniciando Nginx...
ssh %SSH_PARAMS% "systemctl reload nginx"

if %ERRORLEVEL% neq 0 (
    echo [WARNING] Problema al reiniciar Nginx, pero la actualización puede haber sido exitosa
)

echo.
echo ========================================
echo ✅ ACTUALIZACIÓN COMPLETADA
echo ========================================
echo.
echo La aplicación debería estar actualizada en:
echo http://45.236.129.200
echo.
pause
