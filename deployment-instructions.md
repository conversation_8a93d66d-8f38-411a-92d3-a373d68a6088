# Instrucciones de Despliegue - Aplicación React Fuse

## Resumen
Esta guía te ayudará a desplegar la aplicación React Fuse en el servidor nginx (45.236.129.200).

## Archivos Necesarios
1. **Carpeta `build/`** - Contiene la aplicación React construida para producción
2. **`nginx-react-config.conf`** - Configuración de nginx para servir la aplicación
3. **Este archivo de instrucciones**

## Pasos de Despliegue

### 1. Acceso al Servidor
Necesitarás acceso SSH al servidor:
```bash
ssh root@45.236.129.200
```

### 2. Crear Directorio para la Aplicación
```bash
# Crear directorio para la aplicación React
sudo mkdir -p /var/www/html/react-app

# Cambiar permisos
sudo chown -R www-data:www-data /var/www/html/react-app
sudo chmod -R 755 /var/www/html/react-app
```

### 3. Subir Archivos de la Aplicación
Transfiere todos los archivos de la carpeta `build/` al directorio `/var/www/html/react-app/` en el servidor.

Opciones para transferir archivos:
- **SCP**: `scp -r build/* root@45.236.129.200:/var/www/html/react-app/`
- **SFTP**: Usar cliente SFTP como FileZilla
- **rsync**: `rsync -avz build/ root@45.236.129.200:/var/www/html/react-app/`

### 4. Configurar Nginx

#### 4.1 Respaldar configuración actual
```bash
sudo cp /etc/nginx/sites-available/default /etc/nginx/sites-available/default.backup
```

#### 4.2 Aplicar nueva configuración
```bash
# Copiar la nueva configuración
sudo cp nginx-react-config.conf /etc/nginx/sites-available/react-app

# Crear enlace simbólico
sudo ln -sf /etc/nginx/sites-available/react-app /etc/nginx/sites-enabled/react-app

# Remover configuración por defecto si es necesario
sudo rm -f /etc/nginx/sites-enabled/default
```

#### 4.3 Verificar configuración
```bash
# Verificar sintaxis de nginx
sudo nginx -t

# Si hay errores, revisar la configuración
```

### 5. Reiniciar Servicios
```bash
# Reiniciar nginx
sudo systemctl restart nginx

# Verificar estado
sudo systemctl status nginx

# Verificar que nginx esté escuchando en puerto 80
sudo netstat -tlnp | grep :80
```

### 6. Verificar Despliegue
1. Abrir navegador y ir a: `http://45.236.129.200`
2. La aplicación React Fuse debería cargar correctamente
3. Verificar que todas las rutas funcionen (navegación SPA)

### 7. Configuración de Logs (Opcional)
```bash
# Ver logs de acceso
sudo tail -f /var/log/nginx/react-app.access.log

# Ver logs de errores
sudo tail -f /var/log/nginx/react-app.error.log
```

## Solución de Problemas

### Si la aplicación no carga:
1. Verificar permisos de archivos: `ls -la /var/www/html/react-app/`
2. Verificar logs de nginx: `sudo tail -f /var/log/nginx/error.log`
3. Verificar configuración: `sudo nginx -t`

### Si las rutas SPA no funcionan:
- Asegúrate de que la configuración `try_files $uri $uri/ /index.html;` esté presente

### Si hay problemas de rendimiento:
- Verificar que la compresión gzip esté funcionando
- Revisar headers de cache en las herramientas de desarrollador del navegador

## Configuración Adicional (Opcional)

### SSL/HTTPS con Let's Encrypt:
```bash
# Instalar certbot
sudo apt update
sudo apt install certbot python3-certbot-nginx

# Obtener certificado (reemplazar con tu dominio)
sudo certbot --nginx -d tu-dominio.com

# Renovación automática
sudo crontab -e
# Agregar: 0 12 * * * /usr/bin/certbot renew --quiet
```

### Configuración de Firewall:
```bash
# Permitir HTTP y HTTPS
sudo ufw allow 'Nginx Full'
sudo ufw enable
```

## Notas Importantes
- La aplicación está configurada para servirse desde la raíz del dominio
- Los archivos estáticos tienen cache de 1 año para mejor rendimiento
- El archivo index.html no se cachea para asegurar actualizaciones
- La configuración incluye headers de seguridad básicos
