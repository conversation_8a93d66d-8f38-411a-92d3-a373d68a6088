# 🚀 Comandos Rápidos para Clonar con API Token

## 🎯 Tu Situación:
- El proyecto ya no está en el VPS
- Necesitas hacer `git clone` desde cero
- GitHub te pide usuario/contraseña (ya no funciona)
- Tienes API token: `****************************************`

## ⚡ Solución Rápida:

### 1. Conecta al VPS:
```bash
ssh -i ~/.ssh/id_ed25519_server -p 22222 root@45.236.129.200
```

### 2. Copia y pega ESTOS comandos:
```bash
cd /var/www/html
rm -rf Reac_fuse_v2
git clone https://<EMAIL>/nicomagno17/Reac_fuse_v2.git
cd Reac_fuse_v2
git checkout dev_database
git pull origin dev_database
git status
```

### 3. Verificación:
Deberías ver algo como:
```
On branch dev_database
Your branch is up to date with 'origin/dev_database'.
nothing to commit, working tree clean
```

## 🔐 ¿Por qué el API Token?

GitHub eliminó la autenticación con usuario/contraseña en agosto 2021. Ahora SOLO acepta:
- API Tokens (lo que estás usando)
- SSH Keys
- OAuth Apps

## 📝 Formato del Comando:
```bash
git clone https://<EMAIL>/usuario/repositorio.git
```

## 🎉 Una vez configurado:

Para futuras actualizaciones (ya NO necesitarás el token):
```bash
cd /var/www/html/Reac_fuse_v2
git pull origin dev_database
```

El token solo se necesita para el primer `git clone`. Después Git recuerda la configuración.

## ⚠️ Seguridad:
- Nunca compartas tu API token públicamente
- El token que proporcionaste solo funciona para tu cuenta
- Si crees que está comprometido, genera uno nuevo en GitHub

## 🆘 Si algo sale mal:
Ejecuta el script automático:
```bash
./fix_git_vps.sh
```

**¡Listo! Con estos comandos deberías tener tu proyecto funcionando en minutos!** 🚀