import Paper from '@mui/material/Paper';
import Table from '@mui/material/Table';
import TableBody from '@mui/material/TableBody';
import TableCell from '@mui/material/TableCell';
import TableContainer from '@mui/material/TableContainer';
import TableHead from '@mui/material/TableHead';
import TableRow from '@mui/material/TableRow';
import Typography from '@mui/material/Typography';
import Avatar from '@mui/material/Avatar';
import Chip from '@mui/material/Chip';
import Box from '@mui/material/Box';
import { format } from 'date-fns';
import { es } from 'date-fns/locale';

interface User {
	id: number;
	uuid: string;
	email: string;
	display_name: string;
	avatar: string | null;
	role: string;
	status: string;
	created_at: string;
	last_login: string;
}

interface UsersTableProps {
	users: User[];
	loading?: boolean;
}

function UsersTable({ users, loading }: UsersTableProps) {
	const getRoleColor = (role: string) => {
		switch (role) {
			case 'admin':
				return 'error';
			case 'user':
				return 'primary';
			case 'guest':
				return 'default';
			default:
				return 'default';
		}
	};

	const getStatusColor = (status: string) => {
		switch (status) {
			case 'active':
				return 'success';
			case 'inactive':
				return 'default';
			default:
				return 'default';
		}
	};

	const formatDate = (dateString: string) => {
		try {
			return format(new Date(dateString), 'dd MMM yyyy HH:mm', { locale: es });
		} catch {
			return dateString;
		}
	};

	return (
		<Paper className="overflow-hidden shadow-sm">
			<TableContainer className="max-h-[600px]">
				<Table stickyHeader>
					<TableHead>
						<TableRow>
							<TableCell>Usuario</TableCell>
							<TableCell>Email</TableCell>
							<TableCell align="center">Rol</TableCell>
							<TableCell align="center">Estado</TableCell>
							<TableCell>Creado</TableCell>
							<TableCell>Último Acceso</TableCell>
						</TableRow>
					</TableHead>
					<TableBody>
						{users.map((user) => (
							<TableRow
								key={user.id}
								hover
								className="cursor-pointer"
							>
								<TableCell>
									<Box className="flex items-center gap-3">
										<Avatar 
											src={user.avatar || undefined}
											alt={user.display_name}
											className="w-10 h-10"
										>
											{user.display_name.charAt(0)}
										</Avatar>
										<Typography variant="body2" className="font-medium">
											{user.display_name}
										</Typography>
									</Box>
								</TableCell>
								<TableCell>
									<Typography variant="body2" color="text.secondary">
										{user.email}
									</Typography>
								</TableCell>
								<TableCell align="center">
									<Chip
										label={user.role}
										size="small"
										color={getRoleColor(user.role) as any}
									/>
								</TableCell>
								<TableCell align="center">
									<Chip
										label={user.status === 'active' ? 'Activo' : 'Inactivo'}
										size="small"
										color={getStatusColor(user.status) as any}
										variant={user.status === 'active' ? 'filled' : 'outlined'}
									/>
								</TableCell>
								<TableCell>
									<Typography variant="caption" color="text.secondary">
										{formatDate(user.created_at)}
									</Typography>
								</TableCell>
								<TableCell>
									<Typography variant="caption" color="text.secondary">
										{formatDate(user.last_login)}
									</Typography>
								</TableCell>
							</TableRow>
						))}
						{users.length === 0 && !loading && (
							<TableRow>
								<TableCell colSpan={6} align="center">
									<Typography variant="body2" color="text.secondary" className="py-8">
										No hay usuarios registrados
									</Typography>
								</TableCell>
							</TableRow>
						)}
					</TableBody>
				</Table>
			</TableContainer>
		</Paper>
	);
}

export default UsersTable;