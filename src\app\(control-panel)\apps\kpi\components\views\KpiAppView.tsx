'use client';
import FuseLoading from '@fuse/core/FuseLoading';
import FusePageSimple from '@fuse/core/FusePageSimple/FusePageSimple';
import Typography from '@mui/material/Typography';
import Grid from '@mui/material/Grid';
import Alert from '@mui/material/Alert';
import AlertTitle from '@mui/material/AlertTitle';
import Box from '@mui/material/Box';
import RefreshIcon from '@mui/icons-material/Refresh';
import IconButton from '@mui/material/IconButton';
import Tooltip from '@mui/material/Tooltip';
import { Tabs, Tab } from '@mui/material';
import Paper from '@mui/material/Paper';
import GroupIcon from '@mui/icons-material/Group';
import PersonIcon from '@mui/icons-material/Person';
import AdminPanelSettingsIcon from '@mui/icons-material/AdminPanelSettings';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import CancelIcon from '@mui/icons-material/Cancel';
import NotificationsIcon from '@mui/icons-material/Notifications';
import { useState } from 'react';
import * as React from 'react';
import _ from 'lodash';
import { motion } from 'motion/react';
import { useGetAllKpiNotifications } from '../../api/hooks/useGetAllKpiNotifications';
import { useTestDatabaseConnection } from '../../api/hooks/useTestDatabaseConnection';
import { useGetUsers } from '../../api/hooks/useGetUsers';
import { useGetUserStats } from '../../api/hooks/useGetUserStats';
import KpiNotificationCard from '../ui/KpiNotificationCard';
import KpiAppHeader from '../ui/KpiAppHeader';
import UsersTable from '../ui/UsersTable';
import KpiStatWidget from '../ui/KpiStatWidget';

/**
 * KPI App View Component
 */
function KpiAppView() {
	const [tabValue, setTabValue] = useState('overview');

	const { 
		data: notifications, 
		isLoading: notificationsLoading, 
		error: notificationsError, 
		refetch: refetchNotifications,
		isRefetching: notificationsRefetching 
	} = useGetAllKpiNotifications();
	
	const { 
		data: users, 
		isLoading: usersLoading,
		refetch: refetchUsers,
		isRefetching: usersRefetching
	} = useGetUsers();
	
	const { 
		data: userStats,
		isLoading: statsLoading 
	} = useGetUserStats();
	
	const { data: isConnected } = useTestDatabaseConnection();

	const handleRefresh = () => {
		if (tabValue === 'overview' || tabValue === 'users') {
			refetchUsers();
		}
		if (tabValue === 'overview' || tabValue === 'notifications') {
			refetchNotifications();
		}
	};

	function handleTabChange(event: React.SyntheticEvent, value: string) {
		setTabValue(value);
	}

	const isLoading = notificationsLoading || usersLoading || statsLoading;
	const isRefetching = notificationsRefetching || usersRefetching;

	if (isLoading) {
		return <FuseLoading />;
	}

	// Animation variants
	const container = {
		show: {
			transition: {
				staggerChildren: 0.04
			}
		}
	};

	const item = {
		hidden: { opacity: 0, y: 20 },
		show: { opacity: 1, y: 0 }
	};

	return (
		<FusePageSimple
			header={<KpiAppHeader />}
			content={
				<div className="w-full pt-4 sm:pt-6">
					{/* Tabs and Refresh Button */}
					<div className="flex w-full flex-col justify-between gap-2 px-4 sm:flex-row sm:items-center md:px-8">
						<Tabs
							value={tabValue}
							onChange={handleTabChange}
							aria-label="KPI tabs"
						>
							<Tab
								value="overview"
								label="Resumen"
							/>
							<Tab
								value="users"
								label="Usuarios"
							/>
							<Tab
								value="notifications"
								label="Notificaciones"
							/>
						</Tabs>

						<Tooltip title="Actualizar datos">
							<IconButton
								onClick={handleRefresh}
								disabled={isRefetching}
								color="primary"
							>
								<RefreshIcon className={isRefetching ? 'animate-spin' : ''} />
							</IconButton>
						</Tooltip>
					</div>

					<div className="px-4 md:px-8">
						{/* Connection Status Alert */}
						{!isConnected && (
							<Alert severity="warning" className="mb-24">
								<AlertTitle>Advertencia de Conexión</AlertTitle>
								No se pudo establecer conexión con la base de datos MySQL. 
								Se están mostrando datos de ejemplo.
							</Alert>
						)}

						{/* Error Alert */}
						{(notificationsError) && (
							<Alert severity="error" className="mb-24">
								<AlertTitle>Error al cargar datos</AlertTitle>
								{notificationsError.message || 'Ocurrió un error al cargar los datos.'}
							</Alert>
						)}

						{/* Overview Tab */}
						{tabValue === 'overview' && (
							<motion.div
								className="grid w-full min-w-0 grid-cols-1 gap-4 px-4 py-4 sm:grid-cols-2 md:grid-cols-4 md:px-8"
								variants={container}
								initial="hidden"
								animate="show"
							>
								{/* Stats Widgets */}
								<motion.div variants={item}>
									<KpiStatWidget
										title="Usuarios Totales"
										value={userStats?.total || 0}
										icon={<GroupIcon />}
										color="primary"
										description="Usuarios registrados en el sistema"
									/>
								</motion.div>
								<motion.div variants={item}>
									<KpiStatWidget
										title="Usuarios Activos"
										value={userStats?.active || 0}
										icon={<CheckCircleIcon />}
										color="success"
										description="Con acceso activo"
										trend={{ value: 12, isPositive: true }}
									/>
								</motion.div>
								<motion.div variants={item}>
									<KpiStatWidget
										title="Administradores"
										value={userStats?.admins || 0}
										icon={<AdminPanelSettingsIcon />}
										color="error"
										description="Con permisos administrativos"
									/>
								</motion.div>
								<motion.div variants={item}>
									<KpiStatWidget
										title="Notificaciones"
										value={notifications?.length || 0}
										icon={<NotificationsIcon />}
										color="info"
										description="Notificaciones pendientes"
									/>
								</motion.div>

								{/* Recent Users */}
								<motion.div
									variants={item}
									className="sm:col-span-2 md:col-span-4"
								>
									<Paper className="p-24 rounded-xl shadow-sm">
										<Typography variant="h6" className="font-semibold mb-16">
											Usuarios Recientes
										</Typography>
										{users && users.length > 0 ? (
											<UsersTable users={users.slice(0, 5)} />
										) : (
											<Typography color="text.secondary">
												No hay usuarios disponibles
											</Typography>
										)}
									</Paper>
								</motion.div>

								{/* Notifications Preview */}
								<motion.div
									variants={item}
									className="sm:col-span-2 md:col-span-4"
								>
									<Paper className="p-24 rounded-xl shadow-sm">
										<Typography variant="h6" className="font-semibold mb-16">
											Notificaciones Recientes
										</Typography>
										{notifications && notifications.length > 0 ? (
											<div className="grid gap-4 sm:grid-cols-2 lg:grid-cols-3">
												{_.orderBy(notifications, ['time', 'created_at'], ['desc', 'desc'])
													.slice(0, 3)
													.map((notification) => (
														<KpiNotificationCard
															key={notification.id}
															notification={notification}
														/>
													))}
											</div>
										) : (
											<Typography color="text.secondary">
												No hay notificaciones disponibles
											</Typography>
										)}
									</Paper>
								</motion.div>
							</motion.div>
						)}

						{/* Users Tab */}
						{tabValue === 'users' && (
							<motion.div
								className="px-4 py-4 md:px-8"
								variants={container}
								initial="hidden"
								animate="show"
							>
								<motion.div variants={item}>
									<Paper className="p-24 rounded-xl shadow-sm">
										<Box className="flex justify-between items-center mb-24">
											<Typography variant="h6" className="font-semibold">
												Tabla de Usuarios
											</Typography>
											<Typography variant="body2" color="text.secondary">
												Total: {users?.length || 0} usuarios
											</Typography>
										</Box>
										{users && users.length > 0 ? (
											<UsersTable users={users} loading={usersLoading} />
										) : (
											<Box className="flex flex-1 items-center justify-center p-32">
												<div className="text-center">
													<PersonIcon className="text-6xl text-gray-400 mb-16" />
													<Typography
														className="text-xl mb-8"
														color="text.secondary"
													>
														No hay usuarios registrados
													</Typography>
													<Typography
														className="text-sm"
														color="text.disabled"
													>
														La tabla users está vacía o no se pudo acceder a los datos.
													</Typography>
												</div>
											</Box>
										)}
									</Paper>
								</motion.div>

								{/* Database Info */}
								<motion.div variants={item} className="mt-6">
									<Paper className="p-16 rounded-xl shadow-sm">
										<Typography variant="subtitle2" className="font-semibold mb-8">
											Información de la Base de Datos
										</Typography>
										<Typography variant="body2" color="text.secondary" className="mb-4">
											Host: **************:3306
										</Typography>
										<Typography variant="body2" color="text.secondary" className="mb-4">
											Base de datos: renovatio
										</Typography>
										<Typography variant="body2" color="text.secondary">
											Tabla: users
										</Typography>
									</Paper>
								</motion.div>
							</motion.div>
						)}

						{/* Notifications Tab */}
						{tabValue === 'notifications' && (
							<motion.div
								className="grid w-full min-w-0 grid-cols-1 gap-4 px-4 py-4 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 md:px-8"
								variants={container}
								initial="hidden"
								animate="show"
							>
								{/* Notifications Grid */}
								{notifications && notifications.length > 0 ? (
									_.orderBy(notifications, ['time', 'created_at'], ['desc', 'desc']).map((notification) => (
										<motion.div variants={item} key={notification.id}>
											<KpiNotificationCard
												notification={notification}
											/>
										</motion.div>
									))
								) : (
									<Paper className="p-32">
										<Box className="flex flex-1 items-center justify-center">
											<div className="text-center">
												<NotificationsIcon className="text-6xl text-gray-400 mb-16" />
												<Typography
													className="text-xl mb-8"
													color="text.secondary"
												>
													No hay notificaciones disponibles
												</Typography>
												<Typography
													className="text-sm"
													color="text.disabled"
												>
													{isConnected 
														? 'La tabla notifications está vacía o no se pudo acceder a los datos.'
														: 'Verifica la conexión a la base de datos MySQL.'
													}
												</Typography>
											</div>
										</Box>
									</Paper>
								)}

								{/* Database Info */}
								<Box className="mt-32 p-16 bg-gray-50 rounded-lg">
									<Typography variant="subtitle2" className="font-semibold mb-8">
										Información de la Base de Datos
									</Typography>
									<Typography variant="body2" color="text.secondary" className="mb-4">
										Host: **************:3306
									</Typography>
									<Typography variant="body2" color="text.secondary" className="mb-4">
										Base de datos: renovatio
									</Typography>
									<Typography variant="body2" color="text.secondary">
										Tabla: notifications
									</Typography>
								</Box>
							</motion.div>
						)}
					</div>
				</div>
			}
		/>
	);
}

export default KpiAppView;
