{"name": "kpi-api-server", "version": "1.0.0", "description": "API server for KPI dashboard MySQL connectivity", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js"}, "dependencies": {"express": "^4.18.2", "mysql2": "^3.6.0", "cors": "^2.8.5", "dotenv": "^16.3.1"}, "devDependencies": {"nodemon": "^3.0.1"}, "keywords": ["api", "mysql", "kpi", "dashboard"], "author": "Renovatio", "license": "MIT"}