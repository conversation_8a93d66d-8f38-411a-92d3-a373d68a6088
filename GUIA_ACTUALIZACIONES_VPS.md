# Guía de Actualización de Proyecto en VPS

## 🎯 RESPUESTA DIRECTA: ¿Git clone cada vez?

**❌ NO - NUNCA hagas `git clone` para actualizaciones normales**

**✅ SÍ - Solo haz `git clone` la primera vez o si algo está corrupto**

---

## 📋 Flujo de Trabajo Normal

### 🥇 **Primera Vez (Solo una vez en la vida)**
```bash
cd /var/www/html
git clone https://github.com/nicomagno17/Reac_fuse_v2.git
cd Reac_fuse_v2
git checkout dev_database
```

### 🔄 **Actualizaciones Diarias (Normal)**
```bash
cd /var/www/html/Reac_fuse_v2
git pull origin dev_database
```

### 🚨 **Solo si hay problemas/corrupción**
```bash
./fix_git_vps.sh  # Solo cuando algo está roto
```

---

## 📊 Comparación de Métodos

| Método | Cuándo Usar | Velocidad | Riesgo |
|--------|-------------|-----------|--------|
| `git pull` | ✅ **Siempre** para actualizaciones | 🚀 Súper rápido | 🟢 Ninguno |
| `git clone` | ❌ Solo primera vez o reparación | 🐌 Lento (descarga todo) | 🟡 Borra cambios locales |

---

## 🛠️ Scripts Disponibles

### 1. `actualizar_vps.sh` - **USO DIARIO** ⭐
```bash
# Para actualizaciones normales - USA ESTE
./actualizar_vps.sh
```

### 2. `fix_git_vps.sh` - **SOLO EMERGENCIAS** 🚨
```bash
# Solo si el repositorio está corrupto
./fix_git_vps.sh
```

---

## 📝 Comandos Manuales Básicos

### ✅ Actualización Normal (Recomendado)
```bash
cd /var/www/html/Reac_fuse_v2
git status                    # Verificar estado
git pull origin dev_database  # Actualizar código
```

### 🔍 Verificar Antes de Actualizar
```bash
cd /var/www/html/Reac_fuse_v2
git fetch                           # Descargar info de cambios
git log --oneline HEAD..origin/dev_database  # Ver qué cambios hay
git pull origin dev_database        # Aplicar cambios
```

### 📊 Verificar Estado del Proyecto
```bash
cd /var/www/html/Reac_fuse_v2
git status      # Estado actual
git branch      # Rama actual
git log -3      # Últimos 3 commits
```

---

## 🚀 Flujo de Trabajo Recomendado

### Cada vez que quieras actualizar:

1. **Conecta al VPS:**
   ```bash
   ssh -i ~/.ssh/id_ed25519_server -p 22222 root@45.236.129.200
   ```

2. **Actualiza (método simple):**
   ```bash
   cd /var/www/html/Reac_fuse_v2
   git pull origin dev_database
   ```

3. **Si hay cambios en dependencias:**
   ```bash
   npm install
   npm run build
   ```

---

## ⚠️ Qué NO Hacer

❌ **NUNCA** hagas esto para actualizaciones:
```bash
rm -rf Reac_fuse_v2
git clone https://github.com/nicomagno17/Reac_fuse_v2.git
```

❌ **NUNCA** uses `git clone` para actualizar

❌ **NUNCA** borres el directorio para actualizar

---

## 🎯 Resumen Ejecutivo

| Situación | Comando |
|-----------|---------|
| 🔄 Actualización diaria | `git pull origin dev_database` |
| 🆕 Primera vez | `git clone` + setup |
| 🚨 Repositorio corrupto | `./fix_git_vps.sh` |
| 📊 Ver estado | `git status` |
| 🔍 Ver cambios disponibles | `git fetch && git log HEAD..origin/dev_database` |

---

## 💡 Pro Tips

1. **Siempre verifica antes:**
   ```bash
   git status  # Antes de cualquier operación
   ```

2. **Automatiza con alias:**
   ```bash
   # Agregar a ~/.bashrc
   alias update-project="cd /var/www/html/Reac_fuse_v2 && git pull origin dev_database"
   ```

3. **Monitorea cambios:**
   ```bash
   git fetch && git log --oneline HEAD..origin/dev_database
   ```

**🎉 ¡Con `git pull` tendrás actualizaciones súper rápidas y seguras!**