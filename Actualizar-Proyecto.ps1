# Actualizador PowerShell para React Fuse
param(
    [string]$Accion = "menu",
    [string]$Rama = "",
    [switch]$Rebuild = $false,
    [switch]$Forzar = $false
)

# Configuración
$Config = @{
    SSHKey = "$env:USERPROFILE\.ssh\id_ed25519_server"
    SSHPort = "22222"
    SSHUser = "root"
    SSHHost = "**************"
    ProjectPath = "/var/www/html/Reac_fuse_v2"
    BackupPath = "/var/www/html/backups"
}

# Función para mostrar mensajes con colores
function Write-ColorMessage {
    param(
        [string]$Message,
        [string]$Type = "Info"
    )
    
    switch ($Type) {
        "Success" { Write-Host $Message -ForegroundColor Green }
        "Error" { Write-Host $Message -ForegroundColor Red }
        "Warning" { Write-Host $Message -ForegroundColor Yellow }
        "Info" { Write-Host $Message -ForegroundColor Cyan }
        default { Write-Host $Message }
    }
}

# Función para ejecutar comandos SSH
function Invoke-SSHCommand {
    param([string]$Command)
    
    if (-not (Test-Path $Config.SSHKey)) {
        Write-ColorMessage "❌ No se encontró la clave SSH en: $($Config.SSHKey)" "Error"
        return $false
    }
    
    try {
        $sshArgs = @(
            "-i", $Config.SSHKey,
            "-p", $Config.SSHPort,
            "$($Config.SSHUser)@$($Config.SSHHost)",
            $Command
        )
        
        $result = & ssh @sshArgs
        
        if ($LASTEXITCODE -eq 0) {
            Write-Output $result
            return $true
        } else {
            Write-ColorMessage "❌ Error al ejecutar comando SSH" "Error"
            return $false
        }
    }
    catch {
        Write-ColorMessage "❌ Error de conexión SSH: $($_.Exception.Message)" "Error"
        return $false
    }
}

# Función para mostrar el menú
function Show-Menu {
    Clear-Host
    Write-Host "========================================" -ForegroundColor Magenta
    Write-Host "    ACTUALIZADOR REACT FUSE (PowerShell)" -ForegroundColor Magenta
    Write-Host "========================================" -ForegroundColor Magenta
    Write-Host ""
    Write-Host "Servidor: $($Config.SSHHost)" -ForegroundColor Gray
    Write-Host "Proyecto: $($Config.ProjectPath)" -ForegroundColor Gray
    Write-Host ""
    Write-Host "1. 🚀 Actualización rápida (pull)"
    Write-Host "2. 🔧 Actualización con rebuild"
    Write-Host "3. 🌿 Cambiar rama"
    Write-Host "4. 📊 Ver estado del repositorio"
    Write-Host "5. 💾 Restaurar desde backup"
    Write-Host "6. 🧹 Limpiar backups antiguos"
    Write-Host "7. 📋 Listar backups"
    Write-Host "8. ❌ Salir"
    Write-Host ""
}

# Función para actualización rápida
function Update-Quick {
    Write-ColorMessage "🚀 Iniciando actualización rápida..." "Info"
    
    $command = @"
cd $($Config.ProjectPath)
echo '[SERVIDOR] Creando backup...'
mkdir -p $($Config.BackupPath)
BACKUP_NAME=backup_`$(date +%Y%m%d_%H%M%S)
cp -r $($Config.ProjectPath) $($Config.BackupPath)/`$BACKUP_NAME
echo '[SERVIDOR] Backup creado: '`$BACKUP_NAME

echo '[SERVIDOR] Verificando estado...'
git status --porcelain

echo '[SERVIDOR] Haciendo pull...'
CURRENT_BRANCH=`$(git branch --show-current)
git pull origin `$CURRENT_BRANCH

if [ `$? -eq 0 ]; then
    echo '[SERVIDOR] Ajustando permisos...'
    chmod -R 755 $($Config.ProjectPath)/assets/
    chown -R www-data:www-data $($Config.ProjectPath)
    
    echo '[SERVIDOR] Reiniciando Nginx...'
    systemctl reload nginx
    
    echo '[SERVIDOR] ✅ Actualización completada exitosamente'
    echo '[SERVIDOR] 🌐 Aplicación disponible en: http://$($Config.SSHHost)'
else
    echo '[SERVIDOR] ❌ Error durante el pull'
    exit 1
fi
"@
    
    if (Invoke-SSHCommand $command) {
        Write-ColorMessage "✅ Actualización rápida completada exitosamente" "Success"
    } else {
        Write-ColorMessage "❌ Error durante la actualización" "Error"
    }
}

# Función para actualización con rebuild
function Update-Rebuild {
    Write-ColorMessage "🔧 Iniciando actualización con rebuild..." "Warning"
    Write-ColorMessage "⚠️  Esto puede tomar varios minutos..." "Warning"
    
    if (-not $Forzar) {
        $confirm = Read-Host "¿Continuar? (s/n)"
        if ($confirm -ne "s") { return }
    }
    
    $command = @"
cd $($Config.ProjectPath)
echo '[SERVIDOR] Creando backup...'
mkdir -p $($Config.BackupPath)
BACKUP_NAME=backup_rebuild_`$(date +%Y%m%d_%H%M%S)
cp -r $($Config.ProjectPath) $($Config.BackupPath)/`$BACKUP_NAME

echo '[SERVIDOR] Haciendo pull...'
git pull origin `$(git branch --show-current)

echo '[SERVIDOR] Instalando dependencias...'
npm install

echo '[SERVIDOR] Construyendo aplicación...'
npm run build

echo '[SERVIDOR] Ajustando permisos...'
chmod -R 755 $($Config.ProjectPath)/assets/
chown -R www-data:www-data $($Config.ProjectPath)

systemctl reload nginx
echo '[SERVIDOR] ✅ Rebuild completado'
"@
    
    if (Invoke-SSHCommand $command) {
        Write-ColorMessage "✅ Actualización con rebuild completada" "Success"
    } else {
        Write-ColorMessage "❌ Error durante el rebuild" "Error"
    }
}

# Función para cambiar rama
function Switch-Branch {
    param([string]$BranchName)
    
    if (-not $BranchName) {
        $BranchName = Read-Host "Ingresa el nombre de la rama"
    }
    
    if (-not $BranchName) {
        Write-ColorMessage "❌ Nombre de rama requerido" "Error"
        return
    }
    
    Write-ColorMessage "🌿 Cambiando a rama: $BranchName" "Info"
    
    $command = @"
cd $($Config.ProjectPath)
echo '[SERVIDOR] Creando backup...'
mkdir -p $($Config.BackupPath)
BACKUP_NAME=backup_branch_`$(date +%Y%m%d_%H%M%S)
cp -r $($Config.ProjectPath) $($Config.BackupPath)/`$BACKUP_NAME

echo '[SERVIDOR] Obteniendo ramas...'
git fetch origin

echo '[SERVIDOR] Cambiando a rama: $BranchName'
git checkout $BranchName
git pull origin $BranchName

chmod -R 755 $($Config.ProjectPath)/assets/
systemctl reload nginx
echo '[SERVIDOR] ✅ Cambio de rama completado'
"@
    
    if (Invoke-SSHCommand $command) {
        Write-ColorMessage "✅ Cambio de rama completado" "Success"
    } else {
        Write-ColorMessage "❌ Error al cambiar rama" "Error"
    }
}

# Función para ver estado
function Show-Status {
    Write-ColorMessage "📊 Consultando estado del repositorio..." "Info"
    
    $command = @"
cd $($Config.ProjectPath)
echo '[SERVIDOR] Estado del repositorio:'
echo '=================================='
git status
echo ''
echo '[SERVIDOR] Rama actual:'
git branch --show-current
echo ''
echo '[SERVIDOR] Últimos 5 commits:'
git log --oneline -5
echo ''
echo '[SERVIDOR] Archivos modificados:'
git diff --name-only
"@
    
    Invoke-SSHCommand $command | Out-Host
}

# Función principal
function Main {
    switch ($Accion.ToLower()) {
        "quick" { Update-Quick }
        "rebuild" { Update-Rebuild }
        "branch" { Switch-Branch $Rama }
        "status" { Show-Status }
        "menu" {
            do {
                Show-Menu
                $choice = Read-Host "Selecciona una opción (1-8)"
                
                switch ($choice) {
                    "1" { Update-Quick }
                    "2" { Update-Rebuild }
                    "3" { Switch-Branch }
                    "4" { Show-Status }
                    "5" { Write-ColorMessage "Función de restaurar en desarrollo..." "Warning" }
                    "6" { Write-ColorMessage "Función de limpiar en desarrollo..." "Warning" }
                    "7" { Write-ColorMessage "Función de listar en desarrollo..." "Warning" }
                    "8" { Write-ColorMessage "👋 Saliendo..." "Info"; return }
                    default { Write-ColorMessage "❌ Opción inválida" "Error" }
                }
                
                if ($choice -ne "8") {
                    Write-Host ""
                    Read-Host "Presiona Enter para continuar"
                }
            } while ($choice -ne "8")
        }
        default {
            Write-ColorMessage "❌ Acción no válida. Usa: quick, rebuild, branch, status, menu" "Error"
        }
    }
}

# Ejecutar función principal
Main
