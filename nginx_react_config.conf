server {
    listen 80;
    server_name **************;  # Tu IP del VPS
    
    # Directorio raíz donde está tu aplicación React
    root /var/www/html/Reac_fuse_v2/dist;
    index index.html;
    
    # Configuración de MIME types para archivos JavaScript y CSS
    location ~* \.(js)$ {
        add_header Content-Type application/javascript;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
    
    location ~* \.(css)$ {
        add_header Content-Type text/css;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
    
    location ~* \.(woff|woff2|ttf|eot|otf)$ {
        add_header Content-Type font/woff;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
    
    location ~* \.(png|jpg|jpeg|gif|ico|svg)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
    
    # Configuración para archivos estáticos de assets
    location /assets/ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        
        # MIME types específicos para assets
        location ~* /assets/.*\.js$ {
            add_header Content-Type application/javascript;
        }
        
        location ~* /assets/.*\.css$ {
            add_header Content-Type text/css;
        }
    }
    
    # Configuración principal para SPA - todas las rutas van a index.html
    location / {
        try_files $uri $uri/ /index.html;
        
        # Headers de seguridad
        add_header X-Frame-Options DENY;
        add_header X-Content-Type-Options nosniff;
        add_header X-XSS-Protection "1; mode=block";
    }
    
    # Configuración para index.html (no cachear)
    location = /index.html {
        add_header Cache-Control "no-cache, no-store, must-revalidate";
        add_header Pragma "no-cache";
        add_header Expires "0";
    }
    
    # Habilitar compresión
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/javascript
        application/xml+rss
        application/json;
    
    # Configuración de logs
    access_log /var/log/nginx/react_app_access.log;
    error_log /var/log/nginx/react_app_error.log;
    
    # Configuración adicional para desarrollo
    location /api/ {
        # Si tienes un backend API, descomenta y configura esto:
        # proxy_pass http://localhost:3001;  # Puerto de tu backend
        # proxy_http_version 1.1;
        # proxy_set_header Upgrade $http_upgrade;
        # proxy_set_header Connection 'upgrade';
        # proxy_set_header Host $host;
        # proxy_cache_bypass $http_upgrade;
    }
}