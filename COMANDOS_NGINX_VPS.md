# 🚀 Solución MIME Types - Configuración Nginx para React/Vite

## 🎯 Problema Identificado:
El servidor no está sirviendo archivos JavaScript con el MIME type correcto, causando errores de carga en la aplicación React.

## ⚡ Solución Rápida - Comandos para VPS

### 1. Conecta al VPS:
```bash
ssh -i ~/.ssh/id_ed25519_server -p 22222 root@**************
```

### 2. Verificar estado actual:
```bash
# Verificar si Nginx está instalado
nginx -v

# Ver configuración actual
cat /etc/nginx/sites-available/default

# Ver estado del servicio
systemctl status nginx
```

### 3. Configurar Nginx (Opción Rápida):
```bash
# Backup de configuración actual
cp /etc/nginx/sites-available/default /etc/nginx/sites-available/default.backup

# Crear nueva configuración optimizada
cat > /etc/nginx/sites-available/react-app << 'EOF'
server {
    listen 80;
    server_name **************;
    
    root /var/www/html/Reac_fuse_v2/dist;
    index index.html;
    
    # SOLUCION MIME TYPES - Configuración específica para JavaScript
    location ~* \.(js)$ {
        add_header Content-Type application/javascript;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
    
    location ~* \.(css)$ {
        add_header Content-Type text/css;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
    
    # Configuración para archivos de assets (Vite genera estos)
    location /assets/ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        
        location ~* /assets/.*\.js$ {
            add_header Content-Type application/javascript;
        }
        
        location ~* /assets/.*\.css$ {
            add_header Content-Type text/css;
        }
    }
    
    # SPA - Redireccionar todas las rutas a index.html
    location / {
        try_files $uri $uri/ /index.html;
    }
    
    # No cachear index.html
    location = /index.html {
        add_header Cache-Control "no-cache, no-store, must-revalidate";
    }
    
    # Compresión
    gzip on;
    gzip_types text/plain text/css application/javascript text/javascript;
}
EOF

# Desactivar sitio por defecto y activar nuevo
rm -f /etc/nginx/sites-enabled/default
ln -sf /etc/nginx/sites-available/react-app /etc/nginx/sites-enabled/

# Verificar configuración
nginx -t

# Si todo está bien, reiniciar Nginx
systemctl restart nginx
systemctl enable nginx
```

### 4. Verificar que funciona:
```bash
# Ver estado
systemctl status nginx

# Ver logs en tiempo real
tail -f /var/log/nginx/error.log

# Probar MIME type
curl -I http://**************/assets/index.js
```

## 🔧 Configuración Avanzada (Opción Completa)

Si necesitas una configuración más robusta, usa el script automático:

```bash
# Descargar y ejecutar script de configuración
wget -O configurar_nginx.sh https://tu-enlace/configurar_nginx_vps.sh
chmod +x configurar_nginx.sh
./configurar_nginx.sh
```

## 📋 Verificaciones Post-Configuración

### Verificar MIME Types:
```bash
# Probar archivo JavaScript
curl -I http://**************/assets/index-XXXXXX.js

# Debería mostrar:
# Content-Type: application/javascript
```

### Verificar logs:
```bash
# Ver errores
tail -f /var/log/nginx/error.log

# Ver accesos
tail -f /var/log/nginx/access.log
```

### Verificar estructura del proyecto:
```bash
ls -la /var/www/html/Reac_fuse_v2/dist/
ls -la /var/www/html/Reac_fuse_v2/dist/assets/
```

## 🚨 Troubleshooting

### Si el directorio dist no existe:
```bash
cd /var/www/html/Reac_fuse_v2
npm install
npm run build
```

### Si hay errores de permisos:
```bash
chown -R www-data:www-data /var/www/html/Reac_fuse_v2
chmod -R 755 /var/www/html/Reac_fuse_v2
```

### Si Nginx no se reinicia:
```bash
# Ver errores específicos
nginx -t

# Forzar reinicio
systemctl stop nginx
systemctl start nginx
```

## 📊 Comandos de Monitoreo

```bash
# Estado en tiempo real
systemctl status nginx

# Logs en vivo
tail -f /var/log/nginx/error.log

# Verificar configuración
nginx -T

# Recargar configuración sin reiniciar
nginx -s reload
```

## ✅ Resultado Esperado

Después de la configuración:
- ✅ Los archivos .js tendrán Content-Type: application/javascript
- ✅ Los archivos .css tendrán Content-Type: text/css  
- ✅ La aplicación React cargará sin errores de MIME type
- ✅ Las rutas SPA funcionarán correctamente
- ✅ Los assets estarán optimizados con caché

## 🎯 Comando Específico para tu Problema

**Solo la parte de MIME types (solución mínima):**

```bash
# Añadir solo la configuración de MIME types a tu configuración existente
location ~* \.(js)$ {
    add_header Content-Type application/javascript;
}

location ~* \.(css)$ {
    add_header Content-Type text/css;
}
```

**¡Listo! Con esta configuración tu aplicación React debería servir correctamente los archivos JavaScript.** 🎉