#!/bin/bash

# Script para corregir problemas de despliegue en VPS
# Proyecto: React Fuse v2 - Renovatio

set -e  # Salir si hay errores

# Colores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuración
VPS_HOST="**************"
VPS_USER="root"
VPS_PORT="22222"
SSH_KEY="$HOME/.ssh/id_ed25519_server"
PROJECT_PATH="/var/www/html/Reac_fuse_v2"
BACKUP_PATH="/var/www/html/backups"

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Función para ejecutar comandos en el VPS
run_remote() {
    ssh -i "$SSH_KEY" -p "$VPS_PORT" "$VPS_USER@$VPS_HOST" "$1"
}

# Función para copiar archivos al VPS
copy_to_vps() {
    scp -i "$SSH_KEY" -P "$VPS_PORT" -r "$1" "$VPS_USER@$VPS_HOST:$2"
}

print_status "🚀 Iniciando corrección de problemas de despliegue..."

# 1. Verificar conexión SSH
print_status "1. Verificando conexión SSH..."
if run_remote "echo 'Conexión SSH exitosa'"; then
    print_success "Conexión SSH establecida"
else
    print_error "No se pudo conectar al VPS"
    exit 1
fi

# 2. Crear backup del estado actual
print_status "2. Creando backup del estado actual..."
BACKUP_NAME="backup-$(date +%Y%m%d-%H%M%S)"
run_remote "mkdir -p $BACKUP_PATH && cp -r $PROJECT_PATH $BACKUP_PATH/$BACKUP_NAME"
print_success "Backup creado en $BACKUP_PATH/$BACKUP_NAME"

# 3. Verificar estructura de directorios
print_status "3. Verificando estructura de directorios en VPS..."
run_remote "ls -la $PROJECT_PATH/"

# 4. Compilar proyecto localmente si es necesario
print_status "4. Verificando build local..."
if [ ! -d "build" ] || [ ! -f "build/index.html" ]; then
    print_warning "Build local no encontrado o incompleto. Compilando..."
    npm run build
    print_success "Build local completado"
else
    print_success "Build local encontrado"
fi

# 5. Subir archivos actualizados
print_status "5. Subiendo archivos actualizados al VPS..."
copy_to_vps "build/*" "$PROJECT_PATH/"
print_success "Archivos de build subidos"

# 6. Corregir configuración de Nginx
print_status "6. Corrigiendo configuración de Nginx..."

# Crear configuración de Nginx corregida
cat > nginx-fixed.conf << 'EOF'
server {
    listen 80;
    listen [::]:80;
    
    server_name ************** _;
    
    # Directorio donde están los archivos de la aplicación React
    root /var/www/html/Reac_fuse_v2;
    index index.html;
    
    # Incluir tipos MIME
    include /etc/nginx/mime.types;
    
    # Configuración para archivos estáticos
    location / {
        try_files $uri $uri/ /index.html;
        
        # Headers de seguridad
        add_header X-Frame-Options SAMEORIGIN;
        add_header X-XSS-Protection "1; mode=block";
        add_header X-Content-Type-Options nosniff;
    }
    
    # Configuración específica para archivos JavaScript
    location ~* \.js$ {
        add_header Content-Type application/javascript;
        expires 1y;
        add_header Cache-Control "public, immutable";
        access_log off;
    }
    
    # Configuración para archivos CSS
    location ~* \.css$ {
        add_header Content-Type text/css;
        expires 1y;
        add_header Cache-Control "public, immutable";
        access_log off;
    }
    
    # Configuración para fuentes
    location ~* \.(woff|woff2|ttf|eot|otf)$ {
        add_header Content-Type font/woff;
        expires 1y;
        add_header Cache-Control "public, immutable";
        access_log off;
    }
    
    # Configuración para imágenes
    location ~* \.(png|jpg|jpeg|gif|ico|svg)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        access_log off;
    }
    
    # No cachear el index.html para asegurar actualizaciones
    location = /index.html {
        add_header Cache-Control "no-cache, no-store, must-revalidate";
        add_header Pragma "no-cache";
        add_header Expires "0";
    }
    
    # Logs específicos
    access_log /var/log/nginx/react-app.access.log;
    error_log /var/log/nginx/react-app.error.log;
}
EOF

# Subir configuración de Nginx
copy_to_vps "nginx-fixed.conf" "/tmp/"
run_remote "cp /tmp/nginx-fixed.conf /etc/nginx/sites-available/react-app"
print_success "Configuración de Nginx actualizada"

# 7. Verificar y corregir permisos
print_status "7. Corrigiendo permisos de archivos..."
run_remote "chown -R www-data:www-data $PROJECT_PATH"
run_remote "chmod -R 755 $PROJECT_PATH"
print_success "Permisos corregidos"

# 8. Reiniciar servicios
print_status "8. Reiniciando servicios..."
run_remote "nginx -t && systemctl reload nginx"
print_success "Nginx reiniciado"

# 9. Verificar estado de archivos críticos
print_status "9. Verificando archivos críticos..."
run_remote "ls -la $PROJECT_PATH/index.html"
run_remote "ls -la $PROJECT_PATH/assets/ | head -10"

print_success "🎉 Corrección de problemas completada!"
print_status "Puedes verificar la aplicación en: http://$VPS_HOST"

# Limpiar archivos temporales
rm -f nginx-fixed.conf

print_status "📋 Resumen de cambios realizados:"
echo "  ✅ Backup creado en $BACKUP_PATH/$BACKUP_NAME"
echo "  ✅ Archivos de build actualizados"
echo "  ✅ Configuración de Nginx corregida"
echo "  ✅ Permisos de archivos corregidos"
echo "  ✅ Servicios reiniciados"
