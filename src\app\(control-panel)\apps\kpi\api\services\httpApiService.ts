import type { KpiNotification, User, UserStats } from '../types';

// API Base URL - will be configured based on environment
const API_BASE_URL = import.meta.env.PROD
  ? 'http://45.236.129.200:8080/api'
  : 'http://45.236.129.200:8080/api';

/**
 * HTTP API Service for KPI Dashboard
 * Communicates with the backend API server instead of direct database connection
 */
export const httpApiService = {
  /**
   * Test API connection
   */
  testConnection: async (): Promise<boolean> => {
    try {
      const response = await fetch(`${API_BASE_URL}/test-connection`);
      const data = await response.json();
      return data.success;
    } catch (error) {
      console.error('API connection test failed:', error);
      return false;
    }
  },

  /**
   * Get health status
   */
  getHealth: async () => {
    try {
      const response = await fetch(`${API_BASE_URL}/health`);
      return await response.json();
    } catch (error) {
      console.error('Health check failed:', error);
      return { status: 'error', database: 'disconnected' };
    }
  },

  /**
   * Get all notifications
   */
  getNotifications: async (): Promise<KpiNotification[]> => {
    try {
      const response = await fetch(`${API_BASE_URL}/notifications`);
      const data = await response.json();
      
      if (data.success) {
        // Transform API response to match frontend interface
        return data.data.map((notification: any) => ({
          id: notification.id.toString(),
          title: notification.title,
          message: notification.message,
          type: notification.type || 'info',
          time: notification.time || notification.created_at,
          read: notification.read_status === 'read',
          created_at: notification.created_at
        }));
      }
      
      // Return fallback data if API fails
      return getFallbackNotifications();
    } catch (error) {
      console.error('Error fetching notifications from API:', error);
      return getFallbackNotifications();
    }
  },

  /**
   * Get notification statistics
   */
  getNotificationStats: async () => {
    try {
      const response = await fetch(`${API_BASE_URL}/notifications/stats`);
      const data = await response.json();
      
      if (data.success) {
        return data.data;
      }
      
      return { total: 0, unread: 0, read: 0 };
    } catch (error) {
      console.error('Error fetching notification stats:', error);
      return { total: 0, unread: 0, read: 0 };
    }
  },

  /**
   * Get all users
   */
  getUsers: async (): Promise<User[]> => {
    try {
      const response = await fetch(`${API_BASE_URL}/users`);
      const data = await response.json();
      
      if (data.success) {
        // Transform API response to match frontend interface
        return data.data.map((user: any) => ({
          id: user.id,
          uuid: user.id.toString(),
          email: user.email,
          display_name: user.display_name,
          avatar: null,
          role: user.role,
          status: user.status,
          created_at: user.created_at,
          last_login: user.last_login || '31 dic 1969 21:00'
        }));
      }
      
      // Return fallback data if API fails
      return getFallbackUsers();
    } catch (error) {
      console.error('Error fetching users from API:', error);
      return getFallbackUsers();
    }
  },

  /**
   * Get user statistics
   */
  getUserStats: async (): Promise<UserStats> => {
    try {
      const response = await fetch(`${API_BASE_URL}/users/stats`);
      const data = await response.json();
      
      if (data.success) {
        return {
          total: data.data.total,
          active: data.data.active,
          admins: data.data.admins
        };
      }
      
      return { total: 0, active: 0, admins: 0 };
    } catch (error) {
      console.error('Error fetching user stats:', error);
      return { total: 0, active: 0, admins: 0 };
    }
  }
};

/**
 * Fallback notifications data
 */
function getFallbackNotifications(): KpiNotification[] {
  return [
    {
      id: '1',
      title: 'Notificación de prueba 1',
      message: 'Esta es una notificación de prueba desde la base de datos',
      type: 'info',
      time: new Date().toISOString(),
      read: false,
      created_at: new Date().toISOString()
    },
    {
      id: '2',
      title: 'Notificación de prueba 2',
      message: 'Segunda notificación de prueba',
      type: 'warning',
      time: new Date(Date.now() - 3600000).toISOString(),
      read: true,
      created_at: new Date(Date.now() - 3600000).toISOString()
    },
    {
      id: '3',
      title: 'Notificación de prueba 3',
      message: 'Tercera notificación de prueba',
      type: 'success',
      time: new Date(Date.now() - 7200000).toISOString(),
      read: false,
      created_at: new Date(Date.now() - 7200000).toISOString()
    }
  ];
}

/**
 * Fallback users data
 */
function getFallbackUsers(): User[] {
  return [
    {
      id: 1,
      uuid: '1',
      display_name: 'Administrador Renovatio',
      email: '<EMAIL>',
      avatar: null,
      role: 'admin',
      status: 'active',
      created_at: '2025-09-06T00:17:00Z',
      last_login: '31 dic 1969 21:00'
    },
    {
      id: 2,
      uuid: '2',
      display_name: 'John Doe',
      email: '<EMAIL>',
      avatar: null,
      role: 'user',
      status: 'active',
      created_at: '2025-09-06T14:06:00Z',
      last_login: '31 dic 1969 21:00'
    }
  ];
}
