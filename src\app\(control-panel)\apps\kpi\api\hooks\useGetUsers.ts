import { useQuery } from '@tanstack/react-query';
import { httpApiService } from '../services/httpApiService';
import type { User } from '../types';

/**
 * Hook to get all users from API
 */
export function useGetUsers() {
	return useQuery<User[]>({
		queryKey: ['kpi', 'users'],
		queryFn: () => httpApiService.getUsers(),
		staleTime: 30000, // Consider data stale after 30 seconds
		refetchInterval: 60000, // Refetch every minute
		retry: 3,
		retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000)
	});
}